{"name": "arb", "version": "1.0.0", "description": "Cross-exchange arbitrage trading bot using CCXT and TypeScript", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "tsx src/index.ts", "dev": "tsx watch src/index.ts", "cli": "tsx src/cli.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["arbitrage", "trading", "cryptocurrency", "ccxt", "websocket"], "author": "", "license": "ISC", "packageManager": "pnpm@10.13.1", "dependencies": {"@types/node": "^24.1.0", "@types/ws": "^8.18.1", "ccxt": "^4.4.96", "http-proxy-agent": "^7.0.2", "tsx": "^4.20.3", "typescript": "^5.8.3", "ws": "^8.18.3"}}