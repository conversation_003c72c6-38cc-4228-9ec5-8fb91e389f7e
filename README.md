# 跨所套利交易程序

一个使用 CCXT 和 TypeScript 构建的高性能跨交易所套利交易程序，支持实时 WebSocket 价格监控和自动化交易执行。

## ✨ 特性

- 🚀 **高性能**: 使用 WebSocket 实时监控多个交易所价格
- 🔄 **自动化**: 自动检测套利机会并执行交易
- 🛡️ **风险控制**: 内置多层风险管理机制
- 📊 **实时监控**: 实时显示连接状态、套利机会和交易统计
- 🎯 **精确计算**: 考虑手续费的精确利润计算
- 🔧 **高度可配置**: 灵活的配置选项
- 📝 **详细日志**: 结构化日志记录和错误追踪

## 🏗️ 架构

```
src/
├── types/           # TypeScript 类型定义
├── config/          # 配置管理
├── utils/           # 工具函数和日志
├── exchanges/       # 交易所价格监控
├── arbitrage/       # 套利核心逻辑
│   ├── opportunity-detector.ts  # 机会检测
│   ├── trade-executor.ts       # 交易执行
│   └── arbitrage-engine.ts     # 主引擎
└── index.ts         # 程序入口
```

## 🚀 快速开始

### 1. 安装依赖

```bash
# 使用 pnpm 安装依赖
pnpm install
```

### 2. 配置环境变量

```bash
# 复制示例配置文件
cp .env.example .env

# 编辑配置文件，添加你的 API 密钥
nano .env
```

### 3. 配置 API 密钥

在 `.env` 文件中配置你的交易所 API 密钥：

```env
# 启用交易（谨慎使用）
TRADING_ENABLED=false

# Binance API
BINANCE_API_KEY=your_api_key
BINANCE_SECRET=your_secret

# OKX API
OKX_API_KEY=your_api_key
OKX_SECRET=your_secret
OKX_PASSWORD=your_passphrase

# Bybit API
BYBIT_API_KEY=your_api_key
BYBIT_SECRET=your_secret
```

### 4. 运行程序

```bash
# 开发模式（自动重启）
pnpm dev

# 生产模式
pnpm start

# 构建项目
pnpm build
```

## ⚙️ 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `TRADING_ENABLED` | 是否启用实际交易 | `false` |
| `MIN_PROFIT_PERCENTAGE` | 最小利润百分比 | `0.1` |
| `MAX_POSITION_SIZE` | 最大仓位大小 (USDT) | `1000` |
| `SYMBOLS` | 监控的交易对 | `BTC/USDT,ETH/USDT...` |

### 风险控制参数

- **最小利润要求**: 只执行超过设定利润阈值的交易
- **最大仓位控制**: 限制单笔交易的最大金额
- **并发交易限制**: 控制同时进行的交易数量
- **每日损失限制**: 达到每日最大损失后停止交易
- **滑点保护**: 防止价格滑点过大的交易

## 📊 监控界面

程序运行时会显示实时监控界面：

```
🔄 跨所套利程序 - 实时监控
==================================================

📊 当前状态:
   运行状态: ✅ 运行中

🔗 交易所连接:
   binance: ✅ 已连接
   okx: ✅ 已连接
   bybit: ✅ 已连接

💹 活跃交易:
   无活跃交易

🎯 最佳机会:
   BTC/USDT: binance → okx (0.15%)
   ETH/USDT: okx → bybit (0.12%)

📈 性能统计:
   检测到的机会: 45
   执行的交易: 3
   成功率: 100.00%
   总利润: 25.67 USDT
   净利润: 23.45 USDT
```

## 🔧 API 密钥设置

### Binance

1. 登录 [Binance](https://www.binance.com)
2. 进入 API 管理页面
3. 创建新的 API 密钥
4. 启用现货交易权限
5. 设置 IP 白名单（推荐）

### OKX

1. 登录 [OKX](https://www.okx.com)
2. 进入 API 管理
3. 创建 API 密钥
4. 设置交易权限和 IP 限制

### Bybit

1. 登录 [Bybit](https://www.bybit.com)
2. 进入 API 管理
3. 创建 API 密钥
4. 启用现货交易权限

## ⚠️ 重要提醒

### 安全注意事项

1. **API 密钥安全**: 
   - 永远不要在代码中硬编码 API 密钥
   - 使用环境变量存储敏感信息
   - 定期轮换 API 密钥

2. **权限设置**:
   - 只授予必要的交易权限
   - 禁用提现权限
   - 设置 IP 白名单

3. **资金安全**:
   - 从小额资金开始测试
   - 设置合理的风险参数
   - 定期监控账户余额

### 风险提示

- 套利交易存在市场风险
- 网络延迟可能影响交易执行
- 交易所可能暂停交易或调整手续费
- 建议先在测试环境中运行

## 🛠️ 开发

### 项目结构

```
├── src/
│   ├── types/                 # 类型定义
│   ├── config/               # 配置管理
│   ├── utils/                # 工具函数
│   ├── exchanges/            # 交易所接口
│   └── arbitrage/            # 套利逻辑
├── package.json
├── tsconfig.json
└── README.md
```

### 添加新交易所

1. 在 `config/index.ts` 中添加交易所配置
2. 确保 CCXT 支持该交易所的 WebSocket
3. 添加相应的环境变量
4. 测试连接和交易功能

### 自定义策略

可以通过修改 `OpportunityDetector` 类来实现自定义的套利策略：

- 调整利润计算方法
- 添加技术指标过滤
- 实现更复杂的风险控制

## 📄 许可证

ISC License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请创建 Issue 或联系开发者。
