import { EventEmitter } from 'events';
import { 
  ArbitrageOpportunity, 
  MarketSnapshot, 
  PriceData, 
  RiskConfig, 
  ExchangeConfig,
  WebSocketEvent 
} from '../types/index.js';
import { 
  generateId, 
  calculateSpreadPercentage, 
  calculateNetProfit, 
  calculateNetProfitPercentage,
  formatNumber 
} from '../utils/index.js';
import { logger } from '../utils/logger.js';

/**
 * 套利机会检测器
 */
export class OpportunityDetector extends EventEmitter {
  private exchangeFees: Map<string, { maker: number; taker: number }> = new Map();
  private lastOpportunities: Map<string, ArbitrageOpportunity> = new Map();

  constructor(
    private exchangeConfigs: ExchangeConfig[],
    private riskConfig: RiskConfig
  ) {
    super();
    this.initializeExchangeFees();
  }

  /**
   * 初始化交易所手续费
   */
  private initializeExchangeFees(): void {
    for (const config of this.exchangeConfigs) {
      const fees = {
        maker: config.fees?.trading?.maker || 0.001,
        taker: config.fees?.trading?.taker || 0.001
      };
      this.exchangeFees.set(config.id, fees);
      
      logger.debug(`设置交易所手续费: ${config.id}`, fees, { exchange: config.id });
    }
  }

  /**
   * 检测套利机会
   */
  detectOpportunities(snapshots: Map<string, MarketSnapshot>): ArbitrageOpportunity[] {
    const opportunities: ArbitrageOpportunity[] = [];

    for (const [symbol, snapshot] of snapshots) {
      // 检查是否在黑名单中
      if (this.riskConfig.blacklistedSymbols.includes(symbol)) {
        continue;
      }

      // 检查是否在白名单中（如果设置了白名单）
      if (this.riskConfig.whitelistedSymbols && 
          !this.riskConfig.whitelistedSymbols.includes(symbol)) {
        continue;
      }

      const symbolOpportunities = this.detectSymbolOpportunities(symbol, snapshot);
      opportunities.push(...symbolOpportunities);
    }

    // 过滤和排序机会
    const filteredOpportunities = this.filterOpportunities(opportunities);
    
    // 发出新机会事件
    for (const opportunity of filteredOpportunities) {
      this.emitOpportunityEvent(opportunity);
    }

    return filteredOpportunities;
  }

  /**
   * 检测单个交易对的套利机会
   */
  private detectSymbolOpportunities(symbol: string, snapshot: MarketSnapshot): ArbitrageOpportunity[] {
    const opportunities: ArbitrageOpportunity[] = [];
    const exchanges = Array.from(snapshot.exchanges.entries());

    // 比较所有交易所对
    for (let i = 0; i < exchanges.length; i++) {
      for (let j = i + 1; j < exchanges.length; j++) {
        const [buyExchangeId, buyData] = exchanges[i];
        const [sellExchangeId, sellData] = exchanges[j];

        // 检查两个方向的套利机会
        const opportunity1 = this.calculateOpportunity(
          symbol, buyExchangeId, buyData, sellExchangeId, sellData
        );
        
        const opportunity2 = this.calculateOpportunity(
          symbol, sellExchangeId, sellData, buyExchangeId, buyData
        );

        if (opportunity1) opportunities.push(opportunity1);
        if (opportunity2) opportunities.push(opportunity2);
      }
    }

    return opportunities;
  }

  /**
   * 计算套利机会
   */
  private calculateOpportunity(
    symbol: string,
    buyExchangeId: string,
    buyData: PriceData,
    sellExchangeId: string,
    sellData: PriceData
  ): ArbitrageOpportunity | null {
    // 获取价格（买入用ask价格，卖出用bid价格）
    const buyPrice = buyData.ask;
    const sellPrice = sellData.bid;

    // 检查价格有效性
    if (!buyPrice || !sellPrice || buyPrice <= 0 || sellPrice <= 0) {
      return null;
    }

    // 检查是否有利润空间
    if (sellPrice <= buyPrice) {
      return null;
    }

    // 获取手续费
    const buyFees = this.exchangeFees.get(buyExchangeId);
    const sellFees = this.exchangeFees.get(sellExchangeId);
    
    if (!buyFees || !sellFees) {
      logger.warn(`缺少手续费信息: ${buyExchangeId} 或 ${sellExchangeId}`);
      return null;
    }

    // 使用 taker 费率（市价单）
    const buyFee = buyFees.taker;
    const sellFee = sellFees.taker;

    // 计算价差和利润
    const spread = sellPrice - buyPrice;
    const spreadPercentage = calculateSpreadPercentage(buyPrice, sellPrice);
    const netProfitPercentage = calculateNetProfitPercentage(buyPrice, sellPrice, buyFee, sellFee);

    // 检查是否满足最小利润要求
    if (netProfitPercentage < this.riskConfig.minProfitPercentage) {
      return null;
    }

    // 计算建议交易量（基于最大仓位大小）
    const maxVolume = this.riskConfig.maxPositionSize / buyPrice;
    const volume = formatNumber(maxVolume, 8);

    // 计算实际利润
    const netProfit = calculateNetProfit(buyPrice, sellPrice, volume, buyFee, sellFee);

    const opportunity: ArbitrageOpportunity = {
      id: generateId(),
      symbol,
      buyExchange: buyExchangeId,
      sellExchange: sellExchangeId,
      buyPrice: formatNumber(buyPrice, 8),
      sellPrice: formatNumber(sellPrice, 8),
      spread: formatNumber(spread, 8),
      spreadPercentage: formatNumber(spreadPercentage, 4),
      netProfit: formatNumber(netProfit, 4),
      netProfitPercentage: formatNumber(netProfitPercentage, 4),
      volume,
      timestamp: Math.max(buyData.timestamp, sellData.timestamp),
      fees: {
        buyFee,
        sellFee,
        totalFees: buyFee + sellFee
      }
    };

    return opportunity;
  }

  /**
   * 过滤套利机会
   */
  private filterOpportunities(opportunities: ArbitrageOpportunity[]): ArbitrageOpportunity[] {
    // 按净利润百分比排序
    const sorted = opportunities.sort((a, b) => b.netProfitPercentage - a.netProfitPercentage);

    // 去重：同一交易对只保留最佳机会
    const uniqueOpportunities = new Map<string, ArbitrageOpportunity>();
    
    for (const opportunity of sorted) {
      const key = `${opportunity.symbol}-${opportunity.buyExchange}-${opportunity.sellExchange}`;
      
      if (!uniqueOpportunities.has(key)) {
        uniqueOpportunities.set(key, opportunity);
      }
    }

    return Array.from(uniqueOpportunities.values());
  }

  /**
   * 发出机会事件
   */
  private emitOpportunityEvent(opportunity: ArbitrageOpportunity): void {
    const key = `${opportunity.symbol}-${opportunity.buyExchange}-${opportunity.sellExchange}`;
    const lastOpportunity = this.lastOpportunities.get(key);

    // 检查是否是新机会或显著变化
    const isNewOpportunity = !lastOpportunity;
    const isSignificantChange = lastOpportunity && 
      Math.abs(opportunity.netProfitPercentage - lastOpportunity.netProfitPercentage) > 0.01;

    if (isNewOpportunity || isSignificantChange) {
      this.lastOpportunities.set(key, opportunity);
      
      this.emit('websocket_event', {
        type: 'opportunity_detected',
        data: opportunity
      } as WebSocketEvent);

      logger.info(
        `发现套利机会: ${opportunity.symbol}`,
        {
          buyExchange: opportunity.buyExchange,
          sellExchange: opportunity.sellExchange,
          buyPrice: opportunity.buyPrice,
          sellPrice: opportunity.sellPrice,
          netProfitPercentage: `${opportunity.netProfitPercentage}%`,
          netProfit: `${opportunity.netProfit} USDT`
        },
        { 
          symbol: opportunity.symbol,
          opportunityId: opportunity.id
        }
      );
    }
  }

  /**
   * 获取最佳机会
   */
  getBestOpportunities(limit: number = 10): ArbitrageOpportunity[] {
    const opportunities = Array.from(this.lastOpportunities.values());
    return opportunities
      .sort((a, b) => b.netProfitPercentage - a.netProfitPercentage)
      .slice(0, limit);
  }

  /**
   * 获取特定交易对的机会
   */
  getSymbolOpportunities(symbol: string): ArbitrageOpportunity[] {
    const opportunities = Array.from(this.lastOpportunities.values());
    return opportunities
      .filter(opp => opp.symbol === symbol)
      .sort((a, b) => b.netProfitPercentage - a.netProfitPercentage);
  }

  /**
   * 清理过期机会
   */
  cleanupExpiredOpportunities(maxAge: number = 60000): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, opportunity] of this.lastOpportunities) {
      if (now - opportunity.timestamp > maxAge) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      this.lastOpportunities.delete(key);
    }

    if (expiredKeys.length > 0) {
      logger.debug(`清理了 ${expiredKeys.length} 个过期套利机会`);
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    totalOpportunities: number;
    symbolStats: Map<string, number>;
    exchangePairStats: Map<string, number>;
    averageProfitPercentage: number;
  } {
    const opportunities = Array.from(this.lastOpportunities.values());
    const symbolStats = new Map<string, number>();
    const exchangePairStats = new Map<string, number>();
    
    let totalProfitPercentage = 0;

    for (const opportunity of opportunities) {
      // 统计交易对
      const symbolCount = symbolStats.get(opportunity.symbol) || 0;
      symbolStats.set(opportunity.symbol, symbolCount + 1);

      // 统计交易所对
      const pairKey = `${opportunity.buyExchange}-${opportunity.sellExchange}`;
      const pairCount = exchangePairStats.get(pairKey) || 0;
      exchangePairStats.set(pairKey, pairCount + 1);

      totalProfitPercentage += opportunity.netProfitPercentage;
    }

    return {
      totalOpportunities: opportunities.length,
      symbolStats,
      exchangePairStats,
      averageProfitPercentage: opportunities.length > 0 ? 
        formatNumber(totalProfitPercentage / opportunities.length, 4) : 0
    };
  }
}
