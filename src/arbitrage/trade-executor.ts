import ccxt, { type Exchange, type Order } from 'ccxt';
import { EventEmitter } from 'events';
import {
  ArbitrageOpportunity,
  ArbitrageOrder,
  ArbitrageTrade,
  ExchangeConfig,
  RiskConfig,
  WebSocketEvent
} from '../types/index.js';
import { generateId, retry, delay, ConcurrencyLimiter } from '../utils/index.js';
import { logger } from '../utils/logger.js';

/**
 * 交易执行引擎
 */
export class TradeExecutor extends EventEmitter {
  private exchanges: Map<string, Exchange> = new Map();
  private activeTrades: Map<string, ArbitrageTrade> = new Map();
  private concurrencyLimiter: ConcurrencyLimiter;
  private dailyLoss = 0;
  private dailyResetTime = 0;

  constructor(
    private exchangeConfigs: ExchangeConfig[],
    private riskConfig: RiskConfig,
    private tradingConfig: {
      enabled: boolean;
      dryRun: boolean;
      orderTimeout: number;
      retryAttempts: number;
    }
  ) {
    super();
    this.concurrencyLimiter = new ConcurrencyLimiter(riskConfig.maxConcurrentTrades);
    this.initializeExchanges();
    this.resetDailyLoss();
  }

  /**
   * 初始化交易所实例
   */
  private initializeExchanges(): void {
    for (const config of this.exchangeConfigs) {
      try {
        const ExchangeClass = ccxt[config.id as keyof typeof ccxt] as any;
        if (!ExchangeClass) {
          logger.error(`不支持的交易所: ${config.id}`);
          continue;
        }

        const exchange = new ExchangeClass({
          apiKey: config.apiKey,
          secret: config.secret,
          password: config.password,
          sandbox: config.sandbox,
          enableRateLimit: config.enableRateLimit,
          rateLimit: config.rateLimit,
        });

        this.exchanges.set(config.id, exchange);
        logger.info(`已初始化交易所: ${config.name}`, undefined, { exchange: config.id });
      } catch (error) {
        logger.error(`初始化交易所失败: ${config.name}`, error, { exchange: config.id });
      }
    }
  }

  /**
   * 执行套利交易
   */
  async executeArbitrage(opportunity: ArbitrageOpportunity): Promise<ArbitrageTrade | null> {
    // 检查交易是否启用
    if (!this.tradingConfig.enabled) {
      logger.warn('交易功能未启用', undefined, { opportunityId: opportunity.id });
      return null;
    }

    // 检查风险控制
    if (!this.checkRiskLimits(opportunity)) {
      return null;
    }

    // 使用并发限制器
    return this.concurrencyLimiter.execute(async () => {
      return this.executeTradeInternal(opportunity);
    });
  }

  /**
   * 内部交易执行逻辑
   */
  private async executeTradeInternal(opportunity: ArbitrageOpportunity): Promise<ArbitrageTrade | null> {
    const tradeId = generateId();
    const startTime = Date.now();

    logger.info(
      `开始执行套利交易: ${opportunity.symbol}`,
      {
        buyExchange: opportunity.buyExchange,
        sellExchange: opportunity.sellExchange,
        expectedProfit: `${opportunity.netProfitPercentage}%`
      },
      {
        opportunityId: opportunity.id,
        tradeId,
        symbol: opportunity.symbol
      }
    );

    try {
      // 创建买入订单
      const buyOrder = await this.createOrder(
        opportunity.buyExchange,
        opportunity.symbol,
        'buy',
        opportunity.volume,
        opportunity.buyPrice,
        tradeId
      );

      if (!buyOrder) {
        logger.error('创建买入订单失败', undefined, {
          opportunityId: opportunity.id,
          tradeId,
          symbol: opportunity.symbol
        });
        return null;
      }

      // 创建卖出订单
      const sellOrder = await this.createOrder(
        opportunity.sellExchange,
        opportunity.symbol,
        'sell',
        opportunity.volume,
        opportunity.sellPrice,
        tradeId
      );

      if (!sellOrder) {
        logger.error('创建卖出订单失败，取消买入订单', undefined, {
          opportunityId: opportunity.id,
          tradeId,
          symbol: opportunity.symbol
        });
        
        // 取消买入订单
        await this.cancelOrder(buyOrder);
        return null;
      }

      // 创建套利交易记录
      const trade: ArbitrageTrade = {
        id: tradeId,
        opportunityId: opportunity.id,
        symbol: opportunity.symbol,
        buyOrder,
        sellOrder,
        status: 'pending',
        startTime,
      };

      this.activeTrades.set(tradeId, trade);

      // 监控订单执行
      this.monitorTrade(trade);

      // 发出交易事件
      this.emit('websocket_event', {
        type: 'trade_executed',
        data: trade
      } as WebSocketEvent);

      return trade;

    } catch (error) {
      logger.error('执行套利交易失败', error, {
        opportunityId: opportunity.id,
        tradeId,
        symbol: opportunity.symbol
      });
      return null;
    }
  }

  /**
   * 创建订单
   */
  private async createOrder(
    exchangeId: string,
    symbol: string,
    side: 'buy' | 'sell',
    amount: number,
    price: number,
    tradeId: string
  ): Promise<ArbitrageOrder | null> {
    const exchange = this.exchanges.get(exchangeId);
    if (!exchange) {
      logger.error(`交易所不存在: ${exchangeId}`, undefined, { exchange: exchangeId });
      return null;
    }

    const orderId = generateId();

    try {
      // 模拟交易模式
      if (this.tradingConfig.dryRun) {
        logger.info(
          `[模拟] 创建${side}订单: ${symbol}`,
          { amount, price, exchange: exchangeId },
          { exchange: exchangeId, symbol, tradeId }
        );

        return {
          id: orderId,
          opportunityId: tradeId,
          exchange: exchangeId,
          symbol,
          side,
          amount,
          price,
          status: 'closed', // 模拟立即成交
          orderId: `sim_${orderId}`,
          timestamp: Date.now()
        };
      }

      // 实际交易
      const order = await retry(
        () => exchange.createMarketOrder(symbol, side, amount),
        this.tradingConfig.retryAttempts,
        1000
      ) as Order;

      logger.info(
        `创建${side}订单成功: ${symbol}`,
        { orderId: order.id, amount, price },
        { exchange: exchangeId, symbol, tradeId }
      );

      return {
        id: orderId,
        opportunityId: tradeId,
        exchange: exchangeId,
        symbol,
        side,
        amount,
        price,
        status: order.status as any,
        orderId: order.id,
        timestamp: Date.now()
      };

    } catch (error) {
      logger.error(
        `创建${side}订单失败: ${symbol}`,
        error,
        { exchange: exchangeId, symbol, tradeId }
      );
      return null;
    }
  }

  /**
   * 取消订单
   */
  private async cancelOrder(order: ArbitrageOrder): Promise<void> {
    if (this.tradingConfig.dryRun || !order.orderId) {
      logger.info(
        `[模拟] 取消订单: ${order.symbol}`,
        { orderId: order.orderId },
        { exchange: order.exchange, symbol: order.symbol }
      );
      return;
    }

    const exchange = this.exchanges.get(order.exchange);
    if (!exchange) {
      logger.error(`交易所不存在: ${order.exchange}`, undefined, { exchange: order.exchange });
      return;
    }

    try {
      await retry(
        () => exchange.cancelOrder(order.orderId!, order.symbol),
        this.tradingConfig.retryAttempts,
        1000
      );

      order.status = 'canceled';
      logger.info(
        `取消订单成功: ${order.symbol}`,
        { orderId: order.orderId },
        { exchange: order.exchange, symbol: order.symbol }
      );
    } catch (error) {
      logger.error(
        `取消订单失败: ${order.symbol}`,
        error,
        { exchange: order.exchange, symbol: order.symbol }
      );
    }
  }

  /**
   * 监控交易执行
   */
  private async monitorTrade(trade: ArbitrageTrade): Promise<void> {
    const timeout = setTimeout(() => {
      this.handleTradeTimeout(trade);
    }, this.tradingConfig.orderTimeout);

    try {
      // 等待订单完成
      await this.waitForOrderCompletion(trade.buyOrder);
      await this.waitForOrderCompletion(trade.sellOrder);

      clearTimeout(timeout);

      // 计算实际利润
      const actualProfit = this.calculateActualProfit(trade);
      trade.actualProfit = actualProfit.profit;
      trade.actualProfitPercentage = actualProfit.profitPercentage;
      trade.status = 'completed';
      trade.endTime = Date.now();
      trade.duration = trade.endTime - trade.startTime;

      // 更新每日损失
      if (actualProfit.profit < 0) {
        this.dailyLoss += Math.abs(actualProfit.profit);
      }

      logger.info(
        `套利交易完成: ${trade.symbol}`,
        {
          actualProfit: `${actualProfit.profit} USDT`,
          actualProfitPercentage: `${actualProfit.profitPercentage}%`,
          duration: `${trade.duration}ms`
        },
        {
          tradeId: trade.id,
          symbol: trade.symbol
        }
      );

    } catch (error) {
      clearTimeout(timeout);
      trade.status = 'failed';
      trade.endTime = Date.now();
      trade.duration = trade.endTime - trade.startTime;

      logger.error(
        `套利交易失败: ${trade.symbol}`,
        error,
        { tradeId: trade.id, symbol: trade.symbol }
      );
    } finally {
      this.activeTrades.delete(trade.id);
    }
  }

  /**
   * 等待订单完成
   */
  private async waitForOrderCompletion(order: ArbitrageOrder): Promise<void> {
    if (this.tradingConfig.dryRun) {
      // 模拟延迟
      await delay(100);
      return;
    }

    const exchange = this.exchanges.get(order.exchange);
    if (!exchange || !order.orderId) {
      throw new Error(`无法监控订单: ${order.exchange}`);
    }

    while (order.status !== 'closed' && order.status !== 'canceled') {
      await delay(1000);
      
      try {
        const orderStatus = await exchange.fetchOrder(order.orderId, order.symbol);
        order.status = orderStatus.status as any;
      } catch (error) {
        logger.warn(
          `获取订单状态失败: ${order.symbol}`,
          error,
          { exchange: order.exchange, symbol: order.symbol }
        );
      }
    }
  }

  /**
   * 处理交易超时
   */
  private async handleTradeTimeout(trade: ArbitrageTrade): Promise<void> {
    logger.warn(
      `交易超时，取消订单: ${trade.symbol}`,
      undefined,
      { tradeId: trade.id, symbol: trade.symbol }
    );

    await Promise.all([
      this.cancelOrder(trade.buyOrder),
      this.cancelOrder(trade.sellOrder)
    ]);

    trade.status = 'canceled';
    trade.endTime = Date.now();
    trade.duration = trade.endTime - trade.startTime;
    this.activeTrades.delete(trade.id);
  }

  /**
   * 计算实际利润
   */
  private calculateActualProfit(trade: ArbitrageTrade): { profit: number; profitPercentage: number } {
    // 简化计算，实际应该基于真实成交价格和数量
    const buyAmount = trade.buyOrder.amount * trade.buyOrder.price;
    const sellAmount = trade.sellOrder.amount * trade.sellOrder.price;
    const profit = sellAmount - buyAmount;
    const profitPercentage = (profit / buyAmount) * 100;

    return {
      profit: Math.round(profit * 10000) / 10000,
      profitPercentage: Math.round(profitPercentage * 10000) / 10000
    };
  }

  /**
   * 检查风险限制
   */
  private checkRiskLimits(opportunity: ArbitrageOpportunity): boolean {
    // 检查并发交易数
    if (this.activeTrades.size >= this.riskConfig.maxConcurrentTrades) {
      logger.warn(
        `达到最大并发交易数限制: ${this.riskConfig.maxConcurrentTrades}`,
        undefined,
        { opportunityId: opportunity.id }
      );
      return false;
    }

    // 检查每日损失限制
    if (this.dailyLoss >= this.riskConfig.maxDailyLoss) {
      logger.warn(
        `达到每日最大损失限制: ${this.riskConfig.maxDailyLoss} USDT`,
        undefined,
        { opportunityId: opportunity.id }
      );
      return false;
    }

    // 检查最小利润要求
    if (opportunity.netProfitPercentage < this.riskConfig.minProfitPercentage) {
      logger.debug(
        `利润不满足最小要求: ${opportunity.netProfitPercentage}% < ${this.riskConfig.minProfitPercentage}%`,
        undefined,
        { opportunityId: opportunity.id }
      );
      return false;
    }

    return true;
  }

  /**
   * 重置每日损失
   */
  private resetDailyLoss(): void {
    const now = Date.now();
    const today = new Date(now).setHours(0, 0, 0, 0);
    
    if (this.dailyResetTime < today) {
      this.dailyLoss = 0;
      this.dailyResetTime = today;
      logger.info('已重置每日损失统计');
    }

    // 设置下次重置时间
    const tomorrow = today + 24 * 60 * 60 * 1000;
    const timeUntilReset = tomorrow - now;
    
    setTimeout(() => {
      this.resetDailyLoss();
    }, timeUntilReset);
  }

  /**
   * 获取活跃交易
   */
  getActiveTrades(): ArbitrageTrade[] {
    return Array.from(this.activeTrades.values());
  }

  /**
   * 获取交易统计
   */
  getStats(): {
    activeTrades: number;
    dailyLoss: number;
    maxConcurrentTrades: number;
  } {
    return {
      activeTrades: this.activeTrades.size,
      dailyLoss: this.dailyLoss,
      maxConcurrentTrades: this.riskConfig.maxConcurrentTrades
    };
  }
}
