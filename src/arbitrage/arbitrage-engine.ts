import { EventEmitter } from 'events';
import { PriceMonitor } from '../exchanges/price-monitor.js';
import {
  ArbitrageConfig,
  ArbitrageOpportunity,
  ArbitrageTrade,
  PerformanceStats,
  WebSocketEvent
} from '../types/index.js';
import { logger } from '../utils/logger.js';
import { OpportunityDetector } from './opportunity-detector.js';
import { TradeExecutor } from './trade-executor.js';

/**
 * 套利引擎主类
 */
export class ArbitrageEngine extends EventEmitter {
  private priceMonitor: PriceMonitor;
  private opportunityDetector: OpportunityDetector;
  private tradeExecutor: TradeExecutor;
  private isRunning = false;
  private monitoringInterval?: NodeJS.Timeout;
  private cleanupInterval?: NodeJS.Timeout;
  
  // 性能统计
  private stats: PerformanceStats = {
    totalTrades: 0,
    successfulTrades: 0,
    failedTrades: 0,
    totalProfit: 0,
    totalFees: 0,
    netProfit: 0,
    averageProfit: 0,
    winRate: 0,
    averageExecutionTime: 0,
    opportunitiesDetected: 0,
    opportunitiesExecuted: 0
  };

  constructor(private config: ArbitrageConfig) {
    super();
    
    // 初始化组件
    this.priceMonitor = new PriceMonitor(
      config.exchanges,
      config.symbols,
      config.monitoring.websocketReconnectDelay
    );
    
    this.opportunityDetector = new OpportunityDetector(
      config.exchanges,
      config.risk
    );
    
    this.tradeExecutor = new TradeExecutor(
      config.exchanges,
      config.risk,
      config.trading
    );

    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    // 价格监控事件
    this.priceMonitor.on('websocket_event', (event: WebSocketEvent) => {
      this.emit('websocket_event', event);
      
      if (event.type === 'price_update') {
        // 价格更新时检测套利机会
        this.checkOpportunities();
      }
    });

    // 套利机会检测事件
    this.opportunityDetector.on('websocket_event', (event: WebSocketEvent) => {
      this.emit('websocket_event', event);
      
      if (event.type === 'opportunity_detected') {
        this.stats.opportunitiesDetected++;
        
        // 自动执行交易（如果启用）
        if (this.config.trading.enabled) {
          this.executeOpportunity(event.data as ArbitrageOpportunity);
        }
      }
    });

    // 交易执行事件
    this.tradeExecutor.on('websocket_event', (event: WebSocketEvent) => {
      this.emit('websocket_event', event);
      
      if (event.type === 'trade_executed') {
        const trade = event.data as ArbitrageTrade;
        this.stats.opportunitiesExecuted++;
        this.updateTradeStats(trade);
      }
    });
  }

  /**
   * 启动套利引擎
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('套利引擎已在运行中');
      return;
    }

    logger.info('启动套利引擎', {
      exchanges: this.config.exchanges.map(e => e.name),
      symbols: this.config.symbols,
      tradingEnabled: this.config.trading.enabled,
      dryRun: this.config.trading.dryRun
    });

    try {
      this.isRunning = true;

      // 启动价格监控
      await this.priceMonitor.start();

      // 启动定期检查
      this.startPeriodicChecks();

      logger.info('套利引擎启动成功');
      
    } catch (error) {
      this.isRunning = false;
      logger.error('启动套利引擎失败', error);
      throw error;
    }
  }

  /**
   * 停止套利引擎
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    logger.info('停止套利引擎');
    this.isRunning = false;

    // 清理定时器
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = undefined;
    }

    // 停止价格监控
    await this.priceMonitor.stop();

    logger.info('套利引擎已停止');
  }

  /**
   * 启动定期检查
   */
  private startPeriodicChecks(): void {
    // 定期检测套利机会
    this.monitoringInterval = setInterval(() => {
      if (this.isRunning) {
        this.checkOpportunities();
      }
    }, this.config.monitoring.opportunityCheckInterval);

    // 定期清理过期数据
    this.cleanupInterval = setInterval(() => {
      if (this.isRunning) {
        this.opportunityDetector.cleanupExpiredOpportunities();
      }
    }, 60000); // 每分钟清理一次
  }

  /**
   * 检测套利机会
   */
  private checkOpportunities(): void {
    try {
      const snapshots = this.priceMonitor.getAllMarketSnapshots();
      if (snapshots.size === 0) {
        return;
      }

      this.opportunityDetector.detectOpportunities(snapshots);
    } catch (error) {
      logger.error('检测套利机会失败', error);
    }
  }

  /**
   * 执行套利机会
   */
  private async executeOpportunity(opportunity: ArbitrageOpportunity): Promise<void> {
    try {
      const trade = await this.tradeExecutor.executeArbitrage(opportunity);
      if (trade) {
        logger.info(
          `套利交易已提交: ${opportunity.symbol}`,
          { tradeId: trade.id },
          { 
            opportunityId: opportunity.id,
            tradeId: trade.id,
            symbol: opportunity.symbol 
          }
        );
      }
    } catch (error) {
      logger.error(
        `执行套利机会失败: ${opportunity.symbol}`,
        error,
        { opportunityId: opportunity.id, symbol: opportunity.symbol }
      );
    }
  }

  /**
   * 更新交易统计
   */
  private updateTradeStats(trade: ArbitrageTrade): void {
    this.stats.totalTrades++;

    if (trade.status === 'completed') {
      this.stats.successfulTrades++;
      
      if (trade.actualProfit !== undefined) {
        this.stats.totalProfit += trade.actualProfit;
        this.stats.netProfit = this.stats.totalProfit - this.stats.totalFees;
      }
    } else if (trade.status === 'failed') {
      this.stats.failedTrades++;
    }

    if (trade.duration !== undefined) {
      // 更新平均执行时间
      const totalTime = this.stats.averageExecutionTime * (this.stats.totalTrades - 1) + trade.duration;
      this.stats.averageExecutionTime = totalTime / this.stats.totalTrades;
    }

    // 更新其他统计数据
    this.stats.winRate = this.stats.totalTrades > 0 ? 
      (this.stats.successfulTrades / this.stats.totalTrades) * 100 : 0;
    
    this.stats.averageProfit = this.stats.successfulTrades > 0 ? 
      this.stats.totalProfit / this.stats.successfulTrades : 0;
  }

  /**
   * 获取当前状态
   */
  getStatus(): {
    isRunning: boolean;
    connections: Map<string, boolean>;
    activeTrades: ArbitrageTrade[];
    bestOpportunities: ArbitrageOpportunity[];
    stats: PerformanceStats;
  } {
    return {
      isRunning: this.isRunning,
      connections: this.priceMonitor.getConnectionStatus(),
      activeTrades: this.tradeExecutor.getActiveTrades(),
      bestOpportunities: this.opportunityDetector.getBestOpportunities(10),
      stats: { ...this.stats }
    };
  }

  /**
   * 获取特定交易对的机会
   */
  getSymbolOpportunities(symbol: string): ArbitrageOpportunity[] {
    return this.opportunityDetector.getSymbolOpportunities(symbol);
  }

  /**
   * 获取市场快照
   */
  getMarketSnapshot(symbol: string) {
    return this.priceMonitor.getMarketSnapshot(symbol);
  }

  /**
   * 获取所有市场快照
   */
  getAllMarketSnapshots() {
    return this.priceMonitor.getAllMarketSnapshots();
  }

  /**
   * 手动执行套利机会
   */
  async manualExecute(opportunityId: string): Promise<ArbitrageTrade | null> {
    const opportunities = this.opportunityDetector.getBestOpportunities(100);
    const opportunity = opportunities.find(opp => opp.id === opportunityId);
    
    if (!opportunity) {
      logger.warn(`未找到套利机会: ${opportunityId}`);
      return null;
    }

    return this.tradeExecutor.executeArbitrage(opportunity);
  }

  /**
   * 获取详细统计信息
   */
  getDetailedStats(): {
    engine: PerformanceStats;
    opportunities: ReturnType<OpportunityDetector['getStats']>;
    trades: ReturnType<TradeExecutor['getStats']>;
  } {
    return {
      engine: { ...this.stats },
      opportunities: this.opportunityDetector.getStats(),
      trades: this.tradeExecutor.getStats()
    };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalTrades: 0,
      successfulTrades: 0,
      failedTrades: 0,
      totalProfit: 0,
      totalFees: 0,
      netProfit: 0,
      averageProfit: 0,
      winRate: 0,
      averageExecutionTime: 0,
      opportunitiesDetected: 0,
      opportunitiesExecuted: 0
    };
    
    logger.info('已重置统计信息');
  }

  /**
   * 检查引擎是否正在运行
   */
  isEngineRunning(): boolean {
    return this.isRunning;
  }
}
