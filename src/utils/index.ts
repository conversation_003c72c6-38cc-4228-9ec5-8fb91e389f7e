import crypto from 'crypto';

/**
 * 生成唯一 ID
 */
export function generateId(): string {
  return crypto.randomUUID();
}

/**
 * 计算价差百分比
 */
export function calculateSpreadPercentage(buyPrice: number, sellPrice: number): number {
  if (buyPrice <= 0 || sellPrice <= 0) return 0;
  return ((sellPrice - buyPrice) / buyPrice) * 100;
}

/**
 * 计算净利润
 */
export function calculateNetProfit(
  buyPrice: number,
  sellPrice: number,
  amount: number,
  buyFee: number,
  sellFee: number
): number {
  const grossProfit = (sellPrice - buyPrice) * amount;
  const totalFees = (buyPrice * amount * buyFee) + (sellPrice * amount * sellFee);
  return grossProfit - totalFees;
}

/**
 * 计算净利润百分比
 */
export function calculateNetProfitPercentage(
  buyPrice: number,
  sellPrice: number,
  buyFee: number,
  sellFee: number
): number {
  if (buyPrice <= 0) return 0;
  
  const grossProfitPercentage = ((sellPrice - buyPrice) / buyPrice) * 100;
  const totalFeePercentage = (buyFee + sellFee) * 100;
  
  return grossProfitPercentage - totalFeePercentage;
}

/**
 * 格式化数字为指定小数位
 */
export function formatNumber(num: number, decimals: number = 8): number {
  return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
}

/**
 * 格式化百分比
 */
export function formatPercentage(num: number, decimals: number = 4): string {
  return `${formatNumber(num, decimals)}%`;
}

/**
 * 格式化货币
 */
export function formatCurrency(num: number, currency: string = 'USDT', decimals: number = 2): string {
  return `${formatNumber(num, decimals)} ${currency}`;
}

/**
 * 延迟函数
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 重试函数
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxAttempts) {
        throw lastError;
      }
      
      await delay(delayMs * attempt);
    }
  }
  
  throw lastError!;
}

/**
 * 安全的 JSON 解析
 */
export function safeJsonParse<T>(json: string, defaultValue: T): T {
  try {
    return JSON.parse(json);
  } catch {
    return defaultValue;
  }
}

/**
 * 检查对象是否为空
 */
export function isEmpty(obj: any): boolean {
  if (obj == null) return true;
  if (Array.isArray(obj)) return obj.length === 0;
  if (typeof obj === 'object') return Object.keys(obj).length === 0;
  return false;
}

/**
 * 深度克隆对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;
  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  return obj;
}

/**
 * 获取时间戳
 */
export function getTimestamp(): number {
  return Date.now();
}

/**
 * 格式化时间
 */
export function formatTime(timestamp: number): string {
  return new Date(timestamp).toISOString();
}

/**
 * 计算执行时间
 */
export function measureExecutionTime<T>(fn: () => T): { result: T; duration: number } {
  const start = performance.now();
  const result = fn();
  const duration = performance.now() - start;
  return { result, duration };
}

/**
 * 异步计算执行时间
 */
export async function measureAsyncExecutionTime<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
  const start = performance.now();
  const result = await fn();
  const duration = performance.now() - start;
  return { result, duration };
}

/**
 * 限制并发数
 */
export class ConcurrencyLimiter {
  private running = 0;
  private queue: Array<() => void> = [];

  constructor(private limit: number) {}

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      const task = async () => {
        this.running++;
        try {
          const result = await fn();
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          this.running--;
          this.processQueue();
        }
      };

      if (this.running < this.limit) {
        task();
      } else {
        this.queue.push(task);
      }
    });
  }

  private processQueue(): void {
    if (this.queue.length > 0 && this.running < this.limit) {
      const task = this.queue.shift()!;
      task();
    }
  }
}
