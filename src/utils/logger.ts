import { LogLevel, LogEntry } from '../types/index.js';

/**
 * 颜色代码
 */
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

/**
 * 日志级别颜色映射
 */
const levelColors: Record<LogLevel, string> = {
  debug: colors.cyan,
  info: colors.green,
  warn: colors.yellow,
  error: colors.red
};

/**
 * 日志器类
 */
export class Logger {
  private logs: LogEntry[] = [];
  private maxLogs = 10000;

  /**
   * 格式化时间戳
   */
  private formatTimestamp(timestamp: number): string {
    return new Date(timestamp).toISOString();
  }

  /**
   * 格式化日志消息
   */
  private formatMessage(entry: LogEntry): string {
    const timestamp = this.formatTimestamp(entry.timestamp);
    const level = entry.level.toUpperCase().padEnd(5);
    const color = levelColors[entry.level];
    
    let message = `${colors.dim}${timestamp}${colors.reset} ${color}${level}${colors.reset} ${entry.message}`;
    
    if (entry.exchange) {
      message += ` ${colors.blue}[${entry.exchange}]${colors.reset}`;
    }
    
    if (entry.symbol) {
      message += ` ${colors.magenta}[${entry.symbol}]${colors.reset}`;
    }
    
    if (entry.opportunityId) {
      message += ` ${colors.yellow}[OPP:${entry.opportunityId.slice(0, 8)}]${colors.reset}`;
    }
    
    if (entry.tradeId) {
      message += ` ${colors.cyan}[TRADE:${entry.tradeId.slice(0, 8)}]${colors.reset}`;
    }
    
    if (entry.data) {
      message += `\n${colors.dim}${JSON.stringify(entry.data, null, 2)}${colors.reset}`;
    }
    
    return message;
  }

  /**
   * 记录日志
   */
  private log(level: LogLevel, message: string, data?: any, context?: {
    exchange?: string;
    symbol?: string;
    opportunityId?: string;
    tradeId?: string;
  }): void {
    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      message,
      data,
      ...context
    };

    this.logs.push(entry);
    
    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // 输出到控制台
    console.log(this.formatMessage(entry));
  }

  /**
   * Debug 级别日志
   */
  debug(message: string, data?: any, context?: {
    exchange?: string;
    symbol?: string;
    opportunityId?: string;
    tradeId?: string;
  }): void {
    this.log('debug', message, data, context);
  }

  /**
   * Info 级别日志
   */
  info(message: string, data?: any, context?: {
    exchange?: string;
    symbol?: string;
    opportunityId?: string;
    tradeId?: string;
  }): void {
    this.log('info', message, data, context);
  }

  /**
   * Warn 级别日志
   */
  warn(message: string, data?: any, context?: {
    exchange?: string;
    symbol?: string;
    opportunityId?: string;
    tradeId?: string;
  }): void {
    this.log('warn', message, data, context);
  }

  /**
   * Error 级别日志
   */
  error(message: string, data?: any, context?: {
    exchange?: string;
    symbol?: string;
    opportunityId?: string;
    tradeId?: string;
  }): void {
    this.log('error', message, data, context);
  }

  /**
   * 获取日志历史
   */
  getLogs(level?: LogLevel, limit?: number): LogEntry[] {
    let filteredLogs = this.logs;
    
    if (level) {
      filteredLogs = this.logs.filter(log => log.level === level);
    }
    
    if (limit) {
      filteredLogs = filteredLogs.slice(-limit);
    }
    
    return filteredLogs;
  }

  /**
   * 清空日志
   */
  clear(): void {
    this.logs = [];
  }

  /**
   * 获取统计信息
   */
  getStats(): Record<LogLevel, number> {
    const stats: Record<LogLevel, number> = {
      debug: 0,
      info: 0,
      warn: 0,
      error: 0
    };

    for (const log of this.logs) {
      stats[log.level]++;
    }

    return stats;
  }
}

// 全局日志器实例
export const logger = new Logger();
