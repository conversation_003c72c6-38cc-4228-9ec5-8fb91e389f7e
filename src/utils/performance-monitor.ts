import { EventEmitter } from 'events';
import { logger } from './logger.js';

/**
 * 性能监控器
 */
export class PerformanceMonitor extends EventEmitter {
  private metrics: Map<string, number[]> = new Map();
  private timers: Map<string, number> = new Map();
  private counters: Map<string, number> = new Map();
  private isMonitoring = false;
  private monitoringInterval?: NodeJS.Timeout;

  constructor(private reportInterval: number = 60000) {
    super();
  }

  /**
   * 启动性能监控
   */
  start(): void {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    logger.info('启动性能监控');

    // 定期报告性能指标
    this.monitoringInterval = setInterval(() => {
      this.reportMetrics();
    }, this.reportInterval);

    // 监控系统资源
    this.startSystemMonitoring();
  }

  /**
   * 停止性能监控
   */
  stop(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    logger.info('停止性能监控');
  }

  /**
   * 开始计时
   */
  startTimer(name: string): void {
    this.timers.set(name, performance.now());
  }

  /**
   * 结束计时并记录
   */
  endTimer(name: string): number {
    const startTime = this.timers.get(name);
    if (!startTime) {
      logger.warn(`计时器不存在: ${name}`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(name);
    this.recordMetric(name, duration);
    
    return duration;
  }

  /**
   * 记录指标
   */
  recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // 限制存储的数据点数量
    if (values.length > 1000) {
      values.shift();
    }
  }

  /**
   * 增加计数器
   */
  incrementCounter(name: string, value: number = 1): void {
    const current = this.counters.get(name) || 0;
    this.counters.set(name, current + value);
  }

  /**
   * 重置计数器
   */
  resetCounter(name: string): void {
    this.counters.set(name, 0);
  }

  /**
   * 获取指标统计
   */
  getMetricStats(name: string): {
    count: number;
    min: number;
    max: number;
    avg: number;
    latest: number;
  } | null {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) {
      return null;
    }

    const count = values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);
    const avg = values.reduce((sum, val) => sum + val, 0) / count;
    const latest = values[values.length - 1];

    return { count, min, max, avg, latest };
  }

  /**
   * 获取计数器值
   */
  getCounter(name: string): number {
    return this.counters.get(name) || 0;
  }

  /**
   * 获取所有指标
   */
  getAllMetrics(): Map<string, ReturnType<PerformanceMonitor['getMetricStats']>> {
    const result = new Map();
    
    for (const name of this.metrics.keys()) {
      result.set(name, this.getMetricStats(name));
    }
    
    return result;
  }

  /**
   * 获取所有计数器
   */
  getAllCounters(): Map<string, number> {
    return new Map(this.counters);
  }

  /**
   * 报告性能指标
   */
  private reportMetrics(): void {
    const systemMetrics = this.getSystemMetrics();
    const customMetrics = this.getAllMetrics();
    const counters = this.getAllCounters();

    logger.info('性能报告', {
      system: systemMetrics,
      metrics: Object.fromEntries(customMetrics),
      counters: Object.fromEntries(counters)
    });

    // 发出性能报告事件
    this.emit('performance_report', {
      timestamp: Date.now(),
      system: systemMetrics,
      metrics: customMetrics,
      counters
    });
  }

  /**
   * 启动系统监控
   */
  private startSystemMonitoring(): void {
    // 监控内存使用
    setInterval(() => {
      const memUsage = process.memoryUsage();
      this.recordMetric('memory.rss', memUsage.rss / 1024 / 1024); // MB
      this.recordMetric('memory.heapUsed', memUsage.heapUsed / 1024 / 1024); // MB
      this.recordMetric('memory.heapTotal', memUsage.heapTotal / 1024 / 1024); // MB
    }, 5000);

    // 监控 CPU 使用
    setInterval(() => {
      const cpuUsage = process.cpuUsage();
      this.recordMetric('cpu.user', cpuUsage.user / 1000); // ms
      this.recordMetric('cpu.system', cpuUsage.system / 1000); // ms
    }, 5000);

    // 监控事件循环延迟
    setInterval(() => {
      const start = process.hrtime.bigint();
      setImmediate(() => {
        const delay = Number(process.hrtime.bigint() - start) / 1000000; // ms
        this.recordMetric('eventLoop.delay', delay);
      });
    }, 1000);
  }

  /**
   * 获取系统指标
   */
  private getSystemMetrics(): {
    memory: {
      rss: number;
      heapUsed: number;
      heapTotal: number;
    };
    uptime: number;
    pid: number;
  } {
    const memUsage = process.memoryUsage();
    
    return {
      memory: {
        rss: Math.round(memUsage.rss / 1024 / 1024 * 100) / 100, // MB
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024 * 100) / 100, // MB
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024 * 100) / 100, // MB
      },
      uptime: Math.round(process.uptime()),
      pid: process.pid
    };
  }

  /**
   * 清理指标数据
   */
  clearMetrics(): void {
    this.metrics.clear();
    this.counters.clear();
    this.timers.clear();
    logger.info('已清理性能指标数据');
  }
}

// 全局性能监控器实例
export const performanceMonitor = new PerformanceMonitor();
