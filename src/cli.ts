#!/usr/bin/env node

import { ArbitrageEngine } from './arbitrage/arbitrage-engine.js';
import { loadConfig, validateConfig } from './config/index.js';
import { logger } from './utils/logger.js';
import { formatCurrency, formatPercentage } from './utils/index.js';

/**
 * CLI 命令处理器
 */
class ArbitrageCLI {
  private engine?: ArbitrageEngine;

  /**
   * 运行 CLI
   */
  async run(): Promise<void> {
    const args = process.argv.slice(2);
    const command = args[0];

    switch (command) {
      case 'start':
        await this.startCommand();
        break;
      case 'status':
        await this.statusCommand();
        break;
      case 'opportunities':
        await this.opportunitiesCommand();
        break;
      case 'config':
        await this.configCommand();
        break;
      case 'test':
        await this.testCommand();
        break;
      case 'help':
      case '--help':
      case '-h':
        this.showHelp();
        break;
      default:
        console.log('未知命令，使用 --help 查看帮助');
        process.exit(1);
    }
  }

  /**
   * 启动命令
   */
  private async startCommand(): Promise<void> {
    try {
      const config = loadConfig();
      const errors = validateConfig(config);
      
      if (errors.length > 0) {
        console.error('❌ 配置验证失败:');
        errors.forEach(error => console.error(`  - ${error}`));
        process.exit(1);
      }

      this.engine = new ArbitrageEngine(config);
      
      console.log('🚀 启动套利引擎...');
      await this.engine.start();
      
      console.log('✅ 套利引擎启动成功');
      console.log('按 Ctrl+C 停止程序');

      // 保持程序运行
      process.on('SIGINT', async () => {
        console.log('\n🛑 正在停止...');
        if (this.engine) {
          await this.engine.stop();
        }
        process.exit(0);
      });

      // 防止程序退出
      await new Promise(() => {});

    } catch (error) {
      console.error('❌ 启动失败:', error);
      process.exit(1);
    }
  }

  /**
   * 状态命令
   */
  private async statusCommand(): Promise<void> {
    try {
      const config = loadConfig();
      this.engine = new ArbitrageEngine(config);
      
      await this.engine.start();
      
      // 等待一段时间收集数据
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const status = this.engine.getStatus();
      
      console.log('\n📊 套利引擎状态:');
      console.log(`运行状态: ${status.isRunning ? '✅ 运行中' : '❌ 已停止'}`);
      
      console.log('\n🔗 交易所连接:');
      for (const [exchange, connected] of status.connections) {
        console.log(`  ${exchange}: ${connected ? '✅ 已连接' : '❌ 断开'}`);
      }

      console.log('\n💹 活跃交易:');
      if (status.activeTrades.length === 0) {
        console.log('  无活跃交易');
      } else {
        status.activeTrades.forEach(trade => {
          console.log(`  ${trade.symbol}: ${trade.status} (${trade.id.slice(0, 8)})`);
        });
      }

      console.log('\n📈 性能统计:');
      console.log(`  检测到的机会: ${status.stats.opportunitiesDetected}`);
      console.log(`  执行的交易: ${status.stats.opportunitiesExecuted}`);
      console.log(`  成功率: ${formatPercentage(status.stats.winRate)}`);
      console.log(`  总利润: ${formatCurrency(status.stats.totalProfit)}`);
      
      await this.engine.stop();
      
    } catch (error) {
      console.error('❌ 获取状态失败:', error);
      process.exit(1);
    }
  }

  /**
   * 机会命令
   */
  private async opportunitiesCommand(): Promise<void> {
    try {
      const config = loadConfig();
      this.engine = new ArbitrageEngine(config);
      
      console.log('🔍 正在扫描套利机会...');
      await this.engine.start();
      
      // 等待收集数据
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      const status = this.engine.getStatus();
      
      console.log('\n🎯 当前套利机会:');
      if (status.bestOpportunities.length === 0) {
        console.log('  暂无套利机会');
      } else {
        console.log('  交易对        买入交易所    卖出交易所    利润率    预期利润');
        console.log('  ' + '-'.repeat(65));
        
        status.bestOpportunities.slice(0, 10).forEach(opp => {
          const symbol = opp.symbol.padEnd(12);
          const buyEx = opp.buyExchange.padEnd(12);
          const sellEx = opp.sellExchange.padEnd(12);
          const profit = formatPercentage(opp.netProfitPercentage).padEnd(8);
          const amount = formatCurrency(opp.netProfit);
          
          console.log(`  ${symbol} ${buyEx} ${sellEx} ${profit} ${amount}`);
        });
      }
      
      await this.engine.stop();
      
    } catch (error) {
      console.error('❌ 扫描机会失败:', error);
      process.exit(1);
    }
  }

  /**
   * 配置命令
   */
  private async configCommand(): Promise<void> {
    try {
      const config = loadConfig();
      const errors = validateConfig(config);
      
      console.log('\n⚙️  当前配置:');
      console.log(`交易启用: ${config.trading.enabled ? '✅ 是' : '❌ 否'}`);
      console.log(`模拟模式: ${config.trading.dryRun ? '✅ 是' : '❌ 否'}`);
      console.log(`最小利润: ${config.risk.minProfitPercentage}%`);
      console.log(`最大仓位: ${formatCurrency(config.risk.maxPositionSize)}`);
      console.log(`最大并发: ${config.risk.maxConcurrentTrades}`);
      
      console.log('\n🏢 配置的交易所:');
      config.exchanges.forEach(exchange => {
        const hasKeys = exchange.apiKey && exchange.secret;
        console.log(`  ${exchange.name}: ${hasKeys ? '✅ 已配置' : '❌ 未配置'}`);
      });
      
      console.log('\n📈 监控的交易对:');
      config.symbols.forEach(symbol => {
        console.log(`  ${symbol}`);
      });
      
      if (errors.length > 0) {
        console.log('\n❌ 配置错误:');
        errors.forEach(error => console.log(`  - ${error}`));
      } else {
        console.log('\n✅ 配置验证通过');
      }
      
    } catch (error) {
      console.error('❌ 读取配置失败:', error);
      process.exit(1);
    }
  }

  /**
   * 测试命令
   */
  private async testCommand(): Promise<void> {
    try {
      const config = loadConfig();
      
      console.log('🧪 测试交易所连接...');
      
      this.engine = new ArbitrageEngine(config);
      await this.engine.start();
      
      // 等待连接建立
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      const connections = this.engine.getStatus().connections;
      
      console.log('\n🔗 连接测试结果:');
      for (const [exchange, connected] of connections) {
        console.log(`  ${exchange}: ${connected ? '✅ 连接成功' : '❌ 连接失败'}`);
      }
      
      await this.engine.stop();
      
    } catch (error) {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    }
  }

  /**
   * 显示帮助
   */
  private showHelp(): void {
    console.log(`
🔄 跨所套利交易程序 CLI

用法:
  pnpm cli <command>

命令:
  start          启动套利引擎
  status         显示当前状态
  opportunities  扫描套利机会
  config         显示配置信息
  test          测试交易所连接
  help          显示此帮助信息

示例:
  pnpm cli start                # 启动程序
  pnpm cli opportunities        # 扫描机会
  pnpm cli status              # 查看状态
  pnpm cli config              # 查看配置
  pnpm cli test                # 测试连接

环境变量:
  TRADING_ENABLED=true         # 启用实际交易
  MIN_PROFIT_PERCENTAGE=0.1    # 最小利润百分比
  MAX_POSITION_SIZE=1000       # 最大仓位大小

更多信息请查看 README.md
`);
  }
}

// 运行 CLI
const cli = new ArbitrageCLI();
cli.run().catch(error => {
  console.error('CLI 运行失败:', error);
  process.exit(1);
});
