var K=Object.defineProperty;var o=(s,e)=>K(s,"name",{value:e,configurable:!0});import{r as Y}from"./get-pipe-path-BHW2eJdv.mjs";import d from"node:module";import p from"node:path";import{fileURLToPath as O}from"node:url";import{parseTsconfig as V,getTsconfig as Z,createFilesMatcher as q,createPathsMatcher as ee}from"get-tsconfig";import se,{writeSync as te}from"node:fs";import{b as ne,i as ae,a as re}from"./index-7AaEi15b.mjs";import{p as U}from"./client-BQVF1NaW.mjs";import{inspect as oe}from"node:util";import{b as ce,a as ie,c as le,d as fe,o as W}from"./index-gbaejti9.mjs";const R=o(s=>{if(!s.startsWith("data:text/javascript,"))return;const e=s.indexOf("?");if(e===-1)return;const n=new URLSearchParams(s.slice(e+1)).get("filePath");if(n)return n},"getOriginalFilePath"),D=o(s=>{const e=R(s);return e&&(d._cache[e]=d._cache[s],delete d._cache[s],s=e),s},"interopCjsExports"),me=o(s=>{const e=s.indexOf(":");if(e!==-1)return s.slice(0,e)},"getScheme"),N=o(s=>s[0]==="."&&(s[1]==="/"||s[1]==="."||s[2]==="/"),"isRelativePath"),j=o(s=>N(s)||p.isAbsolute(s),"isFilePath"),pe=o(s=>{if(j(s))return!0;const e=me(s);return e&&e!=="node"},"requestAcceptsQuery"),y="file://",he=[".ts",".tsx",".jsx",".mts",".cts"],C=/\.([cm]?ts|[tj]sx)($|\?)/,de=/[/\\].+\.(?:cts|cjs)(?:$|\?)/,ue=/\.json($|\?)/,E=/\/(?:$|\?)/,ge=/^(?:@[^/]+\/)?[^/\\]+$/,Q=`${p.sep}node_modules${p.sep}`;let M,_,S=!1;const A=o(s=>{let e=null;if(s){const a=p.resolve(s);e={path:a,config:V(a)}}else{try{e=Z()}catch{}if(!e)return}M=q(e),_=ee(e),S=e?.config.compilerOptions?.allowJs??!1},"loadTsconfig"),T=o(s=>Array.from(s).length>0?`?${s.toString()}`:"","urlSearchParamsStringify"),Pe=`
//# sourceMappingURL=data:application/json;base64,`,I=o(()=>process.sourceMapsEnabled??!0,"shouldApplySourceMap"),F=o(({code:s,map:e})=>s+Pe+Buffer.from(JSON.stringify(e),"utf8").toString("base64"),"inlineSourceMap"),v=Number(process.env.TSX_DEBUG);v&&(W.enabled=!0,W.supportLevel=3);const J=o(s=>(e,...a)=>{if(!v||e>v)return;const n=`${fe(` tsx P${process.pid} `)} ${s}`,t=a.map(r=>typeof r=="string"?r:oe(r,{colors:!0})).join(" ");te(1,`${n} ${t}
`)},"createLog"),P=J(ce(ie(" CJS "))),je=J(le(" ESM ")),be=[".cts",".mts",".ts",".tsx",".jsx"],xe=[".js",".cjs",".mjs"],k=[".ts",".tsx",".jsx"],$=o((s,e,a,n)=>{const t=Object.getOwnPropertyDescriptor(s,e);t?.set?s[e]=a:(!t||t.configurable)&&Object.defineProperty(s,e,{value:a,enumerable:t?.enumerable||n?.enumerable,writable:n?.writable??(t?t.writable:!0),configurable:n?.configurable??(t?t.configurable:!0)})},"safeSet"),ye=o((s,e,a)=>{const n=e[".js"],t=o((r,c)=>{if(s.enabled===!1)return n(r,c);const[i,f]=c.split("?");if((new URLSearchParams(f).get("namespace")??void 0)!==a)return n(r,c);P(2,"load",{filePath:c}),r.id.startsWith("data:text/javascript,")&&(r.path=p.dirname(i)),U?.send&&U.send({type:"dependency",path:i});const u=be.some(m=>i.endsWith(m)),g=xe.some(m=>i.endsWith(m));if(!u&&!g)return n(r,i);let h=se.readFileSync(i,"utf8");if(i.endsWith(".cjs")){const m=ne(c,h);m&&(h=I()?F(m):m.code)}else if(u||ae(h)){const m=re(h,c,{tsconfigRaw:M?.(i)});h=I()?F(m):m.code}P(1,"loaded",{filePath:i}),r._compile(h,i)},"transformer");$(e,".js",t);for(const r of k)$(e,r,t,{enumerable:!a,writable:!0,configurable:!0});return $(e,".mjs",t,{writable:!0,configurable:!0}),()=>{e[".js"]===t&&(e[".js"]=n);for(const r of[...k,".mjs"])e[r]===t&&delete e[r]}},"createExtensions"),Ee=o(s=>e=>{if((e==="."||e===".."||e.endsWith("/.."))&&(e+="/"),E.test(e)){let a=p.join(e,"index.js");e.startsWith("./")&&(a=`./${a}`);try{return s(a)}catch{}}try{return s(e)}catch(a){const n=a;if(n.code==="MODULE_NOT_FOUND")try{return s(`${e}${p.sep}index.js`)}catch{}throw n}},"createImplicitResolver"),B=[".js",".json"],G=[".ts",".tsx",".jsx"],_e=[...G,...B],Se=[...B,...G],b=Object.create(null);b[".js"]=[".ts",".tsx",".js",".jsx"],b[".jsx"]=[".tsx",".ts",".jsx",".js"],b[".cjs"]=[".cts"],b[".mjs"]=[".mts"];const X=o(s=>{const e=s.split("?"),a=e[1]?`?${e[1]}`:"",[n]=e,t=p.extname(n),r=[],c=b[t];if(c){const f=n.slice(0,-t.length);r.push(...c.map(l=>f+l+a))}const i=!(s.startsWith(y)||j(n))||n.includes(Q)||n.includes("/node_modules/")?Se:_e;return r.push(...i.map(f=>n+f+a)),r},"mapTsExtensions"),w=o((s,e,a)=>{if(P(3,"resolveTsFilename",{request:e,isDirectory:E.test(e),isTsParent:a,allowJs:S}),E.test(e)||!a&&!S)return;const n=X(e);if(n)for(const t of n)try{return s(t)}catch(r){const{code:c}=r;if(c!=="MODULE_NOT_FOUND"&&c!=="ERR_PACKAGE_PATH_NOT_EXPORTED")throw r}},"resolveTsFilename"),ve=o((s,e)=>a=>{if(P(3,"resolveTsFilename",{request:a,isTsParent:e,isFilePath:j(a)}),j(a)){const n=w(s,a,e);if(n)return n}try{return s(a)}catch(n){const t=n;if(t.code==="MODULE_NOT_FOUND"){if(t.path){const c=t.message.match(/^Cannot find module '([^']+)'$/);if(c){const f=c[1],l=w(s,f,e);if(l)return l}const i=t.message.match(/^Cannot find module '([^']+)'. Please verify that the package.json has a valid "main" entry$/);if(i){const f=i[1],l=w(s,f,e);if(l)return l}}const r=w(s,a,e);if(r)return r}throw t}},"createTsExtensionResolver"),z="at cjsPreparseModuleExports (node:internal",we=o(s=>{const e=s.stack.split(`
`).slice(1);return e[1].includes(z)||e[2].includes(z)},"isFromCjsLexer"),Me=o((s,e)=>{const a=s.split("?"),n=new URLSearchParams(a[1]);if(e?.filename){const t=R(e.filename);let r;if(t){const f=t.split("?"),l=f[0];r=f[1],e.filename=l,e.path=p.dirname(l),e.paths=d._nodeModulePaths(e.path),d._cache[l]=e}r||(r=e.filename.split("?")[1]);const i=new URLSearchParams(r).get("namespace");i&&n.append("namespace",i)}return[a[0],n,(t,r)=>(p.isAbsolute(t)&&!t.endsWith(".json")&&!t.endsWith(".node")&&!(r===0&&we(new Error))&&(t+=T(n)),t)]},"preserveQuery"),Te=o((s,e,a)=>{if(s.startsWith(y)&&(s=O(s)),_&&!j(s)&&!e?.filename?.includes(Q)){const n=_(s);for(const t of n)try{return a(t)}catch{}}return a(s)},"resolveTsPaths"),Fe=o((s,e,a)=>(n,t,...r)=>{if(s.enabled===!1)return e(n,t,...r);n=D(n);const[c,i,f]=Me(n,t);if((i.get("namespace")??void 0)!==a)return e(n,t,...r);P(2,"resolve",{request:n,parent:t?.filename??t,restOfArgs:r});let l=o(g=>e(g,t,...r),"nextResolveSimple");l=ve(l,!!(a||t?.filename&&C.test(t.filename))),l=Ee(l);const u=f(Te(c,t,l),r.length);return P(1,"resolved",{request:n,parent:t?.filename??t,resolved:u}),u},"createResolveFilename"),H=o((s,e)=>{if(!e)throw new Error("The current file path (__filename or import.meta.url) must be provided in the second argument of tsx.require()");return s.startsWith(".")?((typeof e=="string"&&e.startsWith(y)||e instanceof URL)&&(e=O(e)),p.resolve(p.dirname(e),s)):s},"resolveContext"),$e=o(s=>{const{sourceMapsEnabled:e}=process,a={enabled:!0};A(process.env.TSX_TSCONFIG_PATH),process.setSourceMapsEnabled(!0);const n=d._resolveFilename,t=Fe(a,n,s?.namespace);d._resolveFilename=t;const r=ye(a,d._extensions,s?.namespace),c=o(()=>{e===!1&&process.setSourceMapsEnabled(!1),a.enabled=!1,d._resolveFilename===t&&(d._resolveFilename=n),r()},"unregister");if(s?.namespace){const i=o((l,u)=>{const g=H(l,u),[h,m]=g.split("?"),x=new URLSearchParams(m);return s.namespace&&!h.startsWith("node:")&&x.set("namespace",s.namespace),Y(h+T(x))},"scopedRequire");c.require=i;const f=o((l,u,g)=>{const h=H(l,u),[m,x]=h.split("?"),L=new URLSearchParams(x);return s.namespace&&!m.startsWith("node:")&&L.set("namespace",s.namespace),t(m+T(L),module,!1,g)},"scopedResolve");c.resolve=f,c.unregister=c}return c},"register");export{D as a,ue as b,de as c,je as d,C as e,y as f,M as g,F as h,ge as i,v as j,pe as k,A as l,_ as m,E as n,X as o,N as p,S as q,$e as r,he as t};
