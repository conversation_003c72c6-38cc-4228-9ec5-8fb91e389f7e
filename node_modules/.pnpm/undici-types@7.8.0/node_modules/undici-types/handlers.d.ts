import Dispatcher from './dispatcher'

export declare class Redirect<PERSON><PERSON>ler implements <PERSON><PERSON>atcher.DispatchHandler {
  constructor (
    dispatch: Dispatcher,
    maxRedirections: number,
    opts: Dispatcher.DispatchOptions,
    handler: Dispatcher.DispatchHandler,
    redirectionLimitReached: boolean
  )
}

export declare class Decorator<PERSON><PERSON>ler implements Dispatcher.DispatchHandler {
  constructor (handler: Dispatcher.DispatchHandler)
}
