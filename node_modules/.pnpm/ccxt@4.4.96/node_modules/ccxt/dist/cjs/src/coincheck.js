'use strict';

var coincheck$1 = require('./abstract/coincheck.js');
var errors = require('./base/errors.js');
var number = require('./base/functions/number.js');
var sha256 = require('./static_dependencies/noble-hashes/sha256.js');

// ----------------------------------------------------------------------------
//  ---------------------------------------------------------------------------
/**
 * @class coincheck
 * @augments Exchange
 */
class coincheck extends coincheck$1 {
    describe() {
        return this.deepExtend(super.describe(), {
            'id': 'coincheck',
            'name': 'coincheck',
            'countries': ['JP', 'ID'],
            'rateLimit': 1500,
            'has': {
                'CORS': undefined,
                'spot': true,
                'margin': false,
                'swap': false,
                'future': false,
                'option': false,
                'addMargin': false,
                'borrowCrossMargin': false,
                'borrowIsolatedMargin': false,
                'borrowMargin': false,
                'cancelOrder': true,
                'closeAllPositions': false,
                'closePosition': false,
                'createOrder': true,
                'createOrderWithTakeProfitAndStopLoss': false,
                'createOrderWithTakeProfitAndStopLossWs': false,
                'createPostOnlyOrder': false,
                'createReduceOnlyOrder': false,
                'fetchBalance': true,
                'fetchBorrowInterest': false,
                'fetchBorrowRate': false,
                'fetchBorrowRateHistories': false,
                'fetchBorrowRateHistory': false,
                'fetchBorrowRates': false,
                'fetchBorrowRatesPerSymbol': false,
                'fetchCrossBorrowRate': false,
                'fetchCrossBorrowRates': false,
                'fetchDeposits': true,
                'fetchFundingHistory': false,
                'fetchFundingInterval': false,
                'fetchFundingIntervals': false,
                'fetchFundingRate': false,
                'fetchFundingRateHistory': false,
                'fetchFundingRates': false,
                'fetchGreeks': false,
                'fetchIndexOHLCV': false,
                'fetchIsolatedBorrowRate': false,
                'fetchIsolatedBorrowRates': false,
                'fetchIsolatedPositions': false,
                'fetchLeverage': false,
                'fetchLeverages': false,
                'fetchLeverageTiers': false,
                'fetchLiquidations': false,
                'fetchLongShortRatio': false,
                'fetchLongShortRatioHistory': false,
                'fetchMarginAdjustmentHistory': false,
                'fetchMarginMode': false,
                'fetchMarginModes': false,
                'fetchMarketLeverageTiers': false,
                'fetchMarkOHLCV': false,
                'fetchMarkPrices': false,
                'fetchMyLiquidations': false,
                'fetchMySettlementHistory': false,
                'fetchMyTrades': true,
                'fetchOpenInterest': false,
                'fetchOpenInterestHistory': false,
                'fetchOpenInterests': false,
                'fetchOpenOrders': true,
                'fetchOption': false,
                'fetchOptionChain': false,
                'fetchOrderBook': true,
                'fetchPosition': false,
                'fetchPositionHistory': false,
                'fetchPositionMode': false,
                'fetchPositions': false,
                'fetchPositionsForSymbol': false,
                'fetchPositionsHistory': false,
                'fetchPositionsRisk': false,
                'fetchPremiumIndexOHLCV': false,
                'fetchSettlementHistory': false,
                'fetchTicker': true,
                'fetchTrades': true,
                'fetchTradingFee': false,
                'fetchTradingFees': true,
                'fetchVolatilityHistory': false,
                'fetchWithdrawals': true,
                'reduceMargin': false,
                'repayCrossMargin': false,
                'repayIsolatedMargin': false,
                'repayMargin': false,
                'setLeverage': false,
                'setMargin': false,
                'setMarginMode': false,
                'setPositionMode': false,
                'ws': true,
            },
            'urls': {
                'logo': 'https://user-images.githubusercontent.com/51840849/87182088-1d6d6380-c2ec-11ea-9c64-8ab9f9b289f5.jpg',
                'api': {
                    'rest': 'https://coincheck.com/api',
                },
                'www': 'https://coincheck.com',
                'doc': 'https://coincheck.com/documents/exchange/api',
                'fees': [
                    'https://coincheck.com/exchange/fee',
                    'https://coincheck.com/info/fee',
                ],
            },
            'api': {
                'public': {
                    'get': [
                        'exchange/orders/rate',
                        'order_books',
                        'rate/{pair}',
                        'ticker',
                        'trades',
                    ],
                },
                'private': {
                    'get': [
                        'accounts',
                        'accounts/balance',
                        'accounts/leverage_balance',
                        'bank_accounts',
                        'deposit_money',
                        'exchange/orders/opens',
                        'exchange/orders/transactions',
                        'exchange/orders/transactions_pagination',
                        'exchange/leverage/positions',
                        'lending/borrows/matches',
                        'send_money',
                        'withdraws',
                    ],
                    'post': [
                        'bank_accounts',
                        'deposit_money/{id}/fast',
                        'exchange/orders',
                        'exchange/transfers/to_leverage',
                        'exchange/transfers/from_leverage',
                        'lending/borrows',
                        'lending/borrows/{id}/repay',
                        'send_money',
                        'withdraws',
                    ],
                    'delete': [
                        'bank_accounts/{id}',
                        'exchange/orders/{id}',
                        'withdraws/{id}',
                    ],
                },
            },
            'markets': {
                'BTC/JPY': this.safeMarketStructure({ 'id': 'btc_jpy', 'symbol': 'BTC/JPY', 'base': 'BTC', 'quote': 'JPY', 'baseId': 'btc', 'quoteId': 'jpy', 'type': 'spot', 'spot': true }),
                // 'ETH/JPY': { 'id': 'eth_jpy', 'symbol': 'ETH/JPY', 'base': 'ETH', 'quote': 'JPY', 'baseId': 'eth', 'quoteId': 'jpy' },
                'ETC/JPY': this.safeMarketStructure({ 'id': 'etc_jpy', 'symbol': 'ETC/JPY', 'base': 'ETC', 'quote': 'JPY', 'baseId': 'etc', 'quoteId': 'jpy', 'type': 'spot', 'spot': true }),
                // 'DAO/JPY': { 'id': 'dao_jpy', 'symbol': 'DAO/JPY', 'base': 'DAO', 'quote': 'JPY', 'baseId': 'dao', 'quoteId': 'jpy' },
                // 'LSK/JPY': { 'id': 'lsk_jpy', 'symbol': 'LSK/JPY', 'base': 'LSK', 'quote': 'JPY', 'baseId': 'lsk', 'quoteId': 'jpy' },
                'FCT/JPY': this.safeMarketStructure({ 'id': 'fct_jpy', 'symbol': 'FCT/JPY', 'base': 'FCT', 'quote': 'JPY', 'baseId': 'fct', 'quoteId': 'jpy', 'type': 'spot', 'spot': true }),
                'MONA/JPY': this.safeMarketStructure({ 'id': 'mona_jpy', 'symbol': 'MONA/JPY', 'base': 'MONA', 'quote': 'JPY', 'baseId': 'mona', 'quoteId': 'jpy', 'type': 'spot', 'spot': true }),
                // 'XMR/JPY': { 'id': 'xmr_jpy', 'symbol': 'XMR/JPY', 'base': 'XMR', 'quote': 'JPY', 'baseId': 'xmr', 'quoteId': 'jpy' },
                // 'REP/JPY': { 'id': 'rep_jpy', 'symbol': 'REP/JPY', 'base': 'REP', 'quote': 'JPY', 'baseId': 'rep', 'quoteId': 'jpy' },
                // 'XRP/JPY': { 'id': 'xrp_jpy', 'symbol': 'XRP/JPY', 'base': 'XRP', 'quote': 'JPY', 'baseId': 'xrp', 'quoteId': 'jpy' },
                // 'ZEC/JPY': { 'id': 'zec_jpy', 'symbol': 'ZEC/JPY', 'base': 'ZEC', 'quote': 'JPY', 'baseId': 'zec', 'quoteId': 'jpy' },
                // 'XEM/JPY': { 'id': 'xem_jpy', 'symbol': 'XEM/JPY', 'base': 'XEM', 'quote': 'JPY', 'baseId': 'xem', 'quoteId': 'jpy' },
                // 'LTC/JPY': { 'id': 'ltc_jpy', 'symbol': 'LTC/JPY', 'base': 'LTC', 'quote': 'JPY', 'baseId': 'ltc', 'quoteId': 'jpy' },
                // 'DASH/JPY': { 'id': 'dash_jpy', 'symbol': 'DASH/JPY', 'base': 'DASH', 'quote': 'JPY', 'baseId': 'dash', 'quoteId': 'jpy' },
                // 'ETH/BTC': { 'id': 'eth_btc', 'symbol': 'ETH/BTC', 'base': 'ETH', 'quote': 'BTC', 'baseId': 'eth', 'quoteId': 'btc' },
                'ETC/BTC': this.safeMarketStructure({ 'id': 'etc_btc', 'symbol': 'ETC/BTC', 'base': 'ETC', 'quote': 'BTC', 'baseId': 'etc', 'quoteId': 'btc', 'type': 'spot', 'spot': true }),
                // 'LSK/BTC': { 'id': 'lsk_btc', 'symbol': 'LSK/BTC', 'base': 'LSK', 'quote': 'BTC', 'baseId': 'lsk', 'quoteId': 'btc' },
                // 'FCT/BTC': { 'id': 'fct_btc', 'symbol': 'FCT/BTC', 'base': 'FCT', 'quote': 'BTC', 'baseId': 'fct', 'quoteId': 'btc' },
                // 'XMR/BTC': { 'id': 'xmr_btc', 'symbol': 'XMR/BTC', 'base': 'XMR', 'quote': 'BTC', 'baseId': 'xmr', 'quoteId': 'btc' },
                // 'REP/BTC': { 'id': 'rep_btc', 'symbol': 'REP/BTC', 'base': 'REP', 'quote': 'BTC', 'baseId': 'rep', 'quoteId': 'btc' },
                // 'XRP/BTC': { 'id': 'xrp_btc', 'symbol': 'XRP/BTC', 'base': 'XRP', 'quote': 'BTC', 'baseId': 'xrp', 'quoteId': 'btc' },
                // 'ZEC/BTC': { 'id': 'zec_btc', 'symbol': 'ZEC/BTC', 'base': 'ZEC', 'quote': 'BTC', 'baseId': 'zec', 'quoteId': 'btc' },
                // 'XEM/BTC': { 'id': 'xem_btc', 'symbol': 'XEM/BTC', 'base': 'XEM', 'quote': 'BTC', 'baseId': 'xem', 'quoteId': 'btc' },
                // 'LTC/BTC': { 'id': 'ltc_btc', 'symbol': 'LTC/BTC', 'base': 'LTC', 'quote': 'BTC', 'baseId': 'ltc', 'quoteId': 'btc' },
                // 'DASH/BTC': { 'id': 'dash_btc', 'symbol': 'DASH/BTC', 'base': 'DASH', 'quote': 'BTC', 'baseId': 'dash', 'quoteId': 'btc' },
            },
            'features': {
                'spot': {
                    'sandbox': false,
                    'createOrder': {
                        'marginMode': false,
                        'triggerPrice': false,
                        'triggerPriceType': undefined,
                        'triggerDirection': false,
                        'stopLossPrice': false,
                        'takeProfitPrice': false,
                        'attachedStopLossTakeProfit': undefined,
                        'timeInForce': {
                            'IOC': false,
                            'FOK': false,
                            'PO': true,
                            'GTD': false,
                        },
                        'hedged': false,
                        'trailing': false,
                        'leverage': false,
                        'marketBuyByCost': false,
                        'marketBuyRequiresPrice': false,
                        'selfTradePrevention': false,
                        'iceberg': false,
                    },
                    'createOrders': undefined,
                    'fetchMyTrades': {
                        'marginMode': false,
                        'limit': undefined,
                        'daysBack': undefined,
                        'untilDays': undefined,
                        'symbolRequired': true,
                    },
                    'fetchOrder': undefined,
                    'fetchOpenOrders': {
                        'marginMode': false,
                        'limit': undefined,
                        'trigger': false,
                        'trailing': false,
                        'symbolRequired': false,
                    },
                    'fetchOrders': undefined,
                    'fetchClosedOrders': undefined,
                    'fetchOHLCV': undefined,
                },
                'swap': {
                    'linear': undefined,
                    'inverse': undefined,
                },
                'future': {
                    'linear': undefined,
                    'inverse': undefined,
                },
            },
            'fees': {
                'trading': {
                    'tierBased': false,
                    'percentage': true,
                    'maker': this.parseNumber('0'),
                    'taker': this.parseNumber('0'),
                },
            },
            'precisionMode': number.TICK_SIZE,
            'exceptions': {
                'exact': {
                    'disabled API Key': errors.AuthenticationError,
                    'invalid authentication': errors.AuthenticationError, // {"success":false,"error":"invalid authentication"}
                },
                'broad': {},
            },
        });
    }
    parseBalance(response) {
        const result = { 'info': response };
        const codes = Object.keys(this.currencies);
        for (let i = 0; i < codes.length; i++) {
            const code = codes[i];
            const currency = this.currency(code);
            const currencyId = currency['id'];
            if (currencyId in response) {
                const account = this.account();
                const reserved = currencyId + '_reserved';
                account['free'] = this.safeString(response, currencyId);
                account['used'] = this.safeString(response, reserved);
                result[code] = account;
            }
        }
        return this.safeBalance(result);
    }
    /**
     * @method
     * @name coincheck#fetchBalance
     * @description query for balance and get the amount of funds available for trading or funds locked in orders
     * @see https://coincheck.com/documents/exchange/api#order-transactions-pagination
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
     */
    async fetchBalance(params = {}) {
        await this.loadMarkets();
        const response = await this.privateGetAccountsBalance(params);
        return this.parseBalance(response);
    }
    /**
     * @method
     * @name coincheck#fetchOpenOrders
     * @description fetch all unfilled currently open orders
     * @see https://coincheck.com/documents/exchange/api#order-opens
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch open orders for
     * @param {int} [limit] the maximum number of  open orders structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    async fetchOpenOrders(symbol = undefined, since = undefined, limit = undefined, params = {}) {
        await this.loadMarkets();
        // Only BTC/JPY is meaningful
        let market = undefined;
        if (symbol !== undefined) {
            market = this.market(symbol);
        }
        const response = await this.privateGetExchangeOrdersOpens(params);
        const rawOrders = this.safeValue(response, 'orders', []);
        const parsedOrders = this.parseOrders(rawOrders, market, since, limit);
        const result = [];
        for (let i = 0; i < parsedOrders.length; i++) {
            result.push(this.extend(parsedOrders[i], { 'status': 'open' }));
        }
        return result;
    }
    parseOrder(order, market = undefined) {
        //
        // fetchOpenOrders
        //
        //     {                        id:  202835,
        //                      "order_type": "buy",
        //                            "rate":  26890,
        //                            "pair": "btc_jpy",
        //                  "pending_amount": "0.5527",
        //       "pending_market_buy_amount":  null,
        //                  "stop_loss_rate":  null,
        //                      "created_at": "2015-01-10T05:55:38.000Z" }
        //
        // todo: add formats for fetchOrder, fetchClosedOrders here
        //
        const id = this.safeString(order, 'id');
        const side = this.safeString(order, 'order_type');
        const timestamp = this.parse8601(this.safeString(order, 'created_at'));
        const amount = this.safeString(order, 'pending_amount');
        const remaining = this.safeString(order, 'pending_amount');
        const price = this.safeString(order, 'rate');
        const status = undefined;
        const marketId = this.safeString(order, 'pair');
        const symbol = this.safeSymbol(marketId, market, '_');
        return this.safeOrder({
            'id': id,
            'clientOrderId': undefined,
            'timestamp': timestamp,
            'datetime': this.iso8601(timestamp),
            'lastTradeTimestamp': undefined,
            'amount': amount,
            'remaining': remaining,
            'filled': undefined,
            'side': side,
            'type': undefined,
            'timeInForce': undefined,
            'postOnly': undefined,
            'status': status,
            'symbol': symbol,
            'price': price,
            'triggerPrice': undefined,
            'cost': undefined,
            'fee': undefined,
            'info': order,
            'average': undefined,
            'trades': undefined,
        }, market);
    }
    /**
     * @method
     * @name coincheck#fetchOrderBook
     * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @see https://coincheck.com/documents/exchange/api#order-book
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {int} [limit] the maximum amount of order book entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    async fetchOrderBook(symbol, limit = undefined, params = {}) {
        await this.loadMarkets();
        const market = this.market(symbol);
        const request = {
            'pair': market['id'],
        };
        const response = await this.publicGetOrderBooks(this.extend(request, params));
        return this.parseOrderBook(response, market['symbol']);
    }
    parseTicker(ticker, market = undefined) {
        //
        // {
        //     "last":4192632.0,
        //     "bid":4192496.0,
        //     "ask":4193749.0,
        //     "high":4332000.0,
        //     "low":4101047.0,
        //     "volume":2313.43191762,
        //     "timestamp":1643374115
        // }
        //
        const symbol = this.safeSymbol(undefined, market);
        const timestamp = this.safeTimestamp(ticker, 'timestamp');
        const last = this.safeString(ticker, 'last');
        return this.safeTicker({
            'symbol': symbol,
            'timestamp': timestamp,
            'datetime': this.iso8601(timestamp),
            'high': this.safeString(ticker, 'high'),
            'low': this.safeString(ticker, 'low'),
            'bid': this.safeString(ticker, 'bid'),
            'bidVolume': undefined,
            'ask': this.safeString(ticker, 'ask'),
            'askVolume': undefined,
            'vwap': undefined,
            'open': undefined,
            'close': last,
            'last': last,
            'previousClose': undefined,
            'change': undefined,
            'percentage': undefined,
            'average': undefined,
            'baseVolume': this.safeString(ticker, 'volume'),
            'quoteVolume': undefined,
            'info': ticker,
        }, market);
    }
    /**
     * @method
     * @name coincheck#fetchTicker
     * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @see https://coincheck.com/documents/exchange/api#ticker
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    async fetchTicker(symbol, params = {}) {
        if (symbol !== 'BTC/JPY') {
            throw new errors.BadSymbol(this.id + ' fetchTicker() supports BTC/JPY only');
        }
        await this.loadMarkets();
        const market = this.market(symbol);
        const request = {
            'pair': market['id'],
        };
        const ticker = await this.publicGetTicker(this.extend(request, params));
        //
        // {
        //     "last":4192632.0,
        //     "bid":4192496.0,
        //     "ask":4193749.0,
        //     "high":4332000.0,
        //     "low":4101047.0,
        //     "volume":2313.43191762,
        //     "timestamp":1643374115
        // }
        //
        return this.parseTicker(ticker, market);
    }
    parseTrade(trade, market = undefined) {
        //
        // fetchTrades (public)
        //
        //      {
        //          "id": "206849494",
        //          "amount": "0.01",
        //          "rate": "5598346.0",
        //          "pair": "btc_jpy",
        //          "order_type": "sell",
        //          "created_at": "2021-12-08T14:10:33.000Z"
        //      }
        //
        // fetchMyTrades (private) - example from docs
        //
        //      {
        //          "id": 38,
        //          "order_id": 49,
        //          "created_at": "2015-11-18T07:02:21.000Z",
        //          "funds": {
        //              "btc": "0.1",
        //              "jpy": "-4096.135"
        //                  },
        //           "pair": "btc_jpy",
        //           "rate": "40900.0",
        //           "fee_currency": "JPY",
        //           "fee": "6.135",
        //           "liquidity": "T",
        //           "side": "buy"
        //      }
        //
        const timestamp = this.parse8601(this.safeString(trade, 'created_at'));
        const id = this.safeString(trade, 'id');
        const priceString = this.safeString(trade, 'rate');
        const marketId = this.safeString(trade, 'pair');
        market = this.safeMarket(marketId, market, '_');
        const baseId = market['baseId'];
        const quoteId = market['quoteId'];
        const symbol = market['symbol'];
        let takerOrMaker = undefined;
        let amountString = undefined;
        let costString = undefined;
        let side = undefined;
        let fee = undefined;
        let orderId = undefined;
        if ('liquidity' in trade) {
            if (this.safeString(trade, 'liquidity') === 'T') {
                takerOrMaker = 'taker';
            }
            else if (this.safeString(trade, 'liquidity') === 'M') {
                takerOrMaker = 'maker';
            }
            const funds = this.safeValue(trade, 'funds', {});
            amountString = this.safeString(funds, baseId);
            costString = this.safeString(funds, quoteId);
            fee = {
                'currency': this.safeString(trade, 'fee_currency'),
                'cost': this.safeString(trade, 'fee'),
            };
            side = this.safeString(trade, 'side');
            orderId = this.safeString(trade, 'order_id');
        }
        else {
            amountString = this.safeString(trade, 'amount');
            side = this.safeString(trade, 'order_type');
        }
        return this.safeTrade({
            'id': id,
            'info': trade,
            'datetime': this.iso8601(timestamp),
            'timestamp': timestamp,
            'symbol': symbol,
            'type': undefined,
            'side': side,
            'order': orderId,
            'takerOrMaker': takerOrMaker,
            'price': priceString,
            'amount': amountString,
            'cost': costString,
            'fee': fee,
        }, market);
    }
    /**
     * @method
     * @name coincheck#fetchMyTrades
     * @description fetch all trades made by the user
     * @see https://coincheck.com/documents/exchange/api#order-transactions-pagination
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch trades for
     * @param {int} [limit] the maximum number of trades structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
     */
    async fetchMyTrades(symbol = undefined, since = undefined, limit = undefined, params = {}) {
        await this.loadMarkets();
        const market = this.market(symbol);
        const request = {};
        if (limit !== undefined) {
            request['limit'] = limit;
        }
        const response = await this.privateGetExchangeOrdersTransactionsPagination(this.extend(request, params));
        //
        //      {
        //          "success": true,
        //          "data": [
        //                      {
        //                          "id": 38,
        //                          "order_id": 49,
        //                          "created_at": "2015-11-18T07:02:21.000Z",
        //                          "funds": {
        //                              "btc": "0.1",
        //                              "jpy": "-4096.135"
        //                                  },
        //                          "pair": "btc_jpy",
        //                          "rate": "40900.0",
        //                          "fee_currency": "JPY",
        //                          "fee": "6.135",
        //                          "liquidity": "T",
        //                          "side": "buy"
        //                       },
        //                  ]
        //      }
        //
        const transactions = this.safeList(response, 'data', []);
        return this.parseTrades(transactions, market, since, limit);
    }
    /**
     * @method
     * @name coincheck#fetchTrades
     * @description get the list of most recent trades for a particular symbol
     * @see https://coincheck.com/documents/exchange/api#public-trades
     * @param {string} symbol unified symbol of the market to fetch trades for
     * @param {int} [since] timestamp in ms of the earliest trade to fetch
     * @param {int} [limit] the maximum amount of trades to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
     */
    async fetchTrades(symbol, since = undefined, limit = undefined, params = {}) {
        await this.loadMarkets();
        const market = this.market(symbol);
        const request = {
            'pair': market['id'],
        };
        if (limit !== undefined) {
            request['limit'] = limit;
        }
        const response = await this.publicGetTrades(this.extend(request, params));
        //
        //      {
        //          "id": "206849494",
        //          "amount": "0.01",
        //          "rate": "5598346.0",
        //          "pair": "btc_jpy",
        //          "order_type": "sell",
        //          "created_at": "2021-12-08T14:10:33.000Z"
        //      }
        //
        const data = this.safeList(response, 'data', []);
        return this.parseTrades(data, market, since, limit);
    }
    /**
     * @method
     * @name coincheck#fetchTradingFees
     * @description fetch the trading fees for multiple markets
     * @see https://coincheck.com/documents/exchange/api#account-info
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure} indexed by market symbols
     */
    async fetchTradingFees(params = {}) {
        await this.loadMarkets();
        const response = await this.privateGetAccounts(params);
        //
        //     {
        //         "success": true,
        //         "id": "7487995",
        //         "email": "<EMAIL>",
        //         "identity_status": "identity_pending",
        //         "bitcoin_address": null,
        //         "lending_leverage": "4",
        //         "taker_fee": "0.0",
        //         "maker_fee": "0.0",
        //         "exchange_fees": {
        //           "btc_jpy": { taker_fee: '0.0', maker_fee: "0.0" },
        //           "etc_jpy": { taker_fee: '0.0', maker_fee: "0.0" },
        //           "fct_jpy": { taker_fee: '0.0', maker_fee: "0.0" },
        //           "mona_jpy": { taker_fee: '0.0', maker_fee: "0.0" },
        //           "plt_jpy": { taker_fee: '0.0', maker_fee: "0.0" }
        //         }
        //     }
        //
        const fees = this.safeValue(response, 'exchange_fees', {});
        const result = {};
        for (let i = 0; i < this.symbols.length; i++) {
            const symbol = this.symbols[i];
            const market = this.market(symbol);
            const fee = this.safeValue(fees, market['id'], {});
            result[symbol] = {
                'info': fee,
                'symbol': symbol,
                'maker': this.safeNumber(fee, 'maker_fee'),
                'taker': this.safeNumber(fee, 'taker_fee'),
                'percentage': true,
                'tierBased': false,
            };
        }
        return result;
    }
    /**
     * @method
     * @name coincheck#createOrder
     * @description create a trade order
     * @see https://coincheck.com/documents/exchange/api#order-new
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} type 'market' or 'limit'
     * @param {string} side 'buy' or 'sell'
     * @param {float} amount how much of currency you want to trade in units of base currency
     * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    async createOrder(symbol, type, side, amount, price = undefined, params = {}) {
        await this.loadMarkets();
        const market = this.market(symbol);
        const request = {
            'pair': market['id'],
        };
        if (type === 'market') {
            request['order_type'] = type + '_' + side;
            if (side === 'sell') {
                request['amount'] = amount;
            }
            else {
                const cost = this.safeNumber(params, 'cost');
                params = this.omit(params, 'cost');
                if (cost !== undefined) {
                    throw new errors.ArgumentsRequired(this.id + ' createOrder() : you should use "cost" parameter instead of "amount" argument to create market buy orders');
                }
                request['market_buy_amount'] = cost;
            }
        }
        else {
            request['order_type'] = side;
            request['rate'] = price;
            request['amount'] = amount;
        }
        const response = await this.privatePostExchangeOrders(this.extend(request, params));
        const id = this.safeString(response, 'id');
        return this.safeOrder({
            'id': id,
            'info': response,
        }, market);
    }
    /**
     * @method
     * @name coincheck#cancelOrder
     * @description cancels an open order
     * @see https://coincheck.com/documents/exchange/api#order-cancel
     * @param {string} id order id
     * @param {string} symbol not used by coincheck cancelOrder ()
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    async cancelOrder(id, symbol = undefined, params = {}) {
        const request = {
            'id': id,
        };
        const response = await this.privateDeleteExchangeOrdersId(this.extend(request, params));
        //
        //    {
        //        "success": true,
        //        "id": 12345
        //    }
        //
        return this.parseOrder(response);
    }
    /**
     * @method
     * @name coincheck#fetchDeposits
     * @description fetch all deposits made to an account
     * @see https://coincheck.com/documents/exchange/api#account-deposits
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch deposits for
     * @param {int} [limit] the maximum number of deposits structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    async fetchDeposits(code = undefined, since = undefined, limit = undefined, params = {}) {
        await this.loadMarkets();
        let currency = undefined;
        const request = {};
        if (code !== undefined) {
            currency = this.currency(code);
            request['currency'] = currency['id'];
        }
        if (limit !== undefined) {
            request['limit'] = limit;
        }
        const response = await this.privateGetDepositMoney(this.extend(request, params));
        // {
        //   "success": true,
        //   "deposits": [
        //     {
        //       "id": 2,
        //       "amount": "0.05",
        //       "currency": "BTC",
        //       "address": "**********************************",
        //       "status": "confirmed",
        //       "confirmed_at": "2015-06-13T08:29:18.000Z",
        //       "created_at": "2015-06-13T08:22:18.000Z"
        //     },
        //     {
        //       "id": 1,
        //       "amount": "0.01",
        //       "currency": "BTC",
        //       "address": "**********************************",
        //       "status": "received",
        //       "confirmed_at": "2015-06-13T08:21:18.000Z",
        //       "created_at": "2015-06-13T08:21:18.000Z"
        //     }
        //   ]
        // }
        const data = this.safeList(response, 'deposits', []);
        return this.parseTransactions(data, currency, since, limit, { 'type': 'deposit' });
    }
    /**
     * @method
     * @name coincheck#fetchWithdrawals
     * @description fetch all withdrawals made from an account
     * @see https://coincheck.com/documents/exchange/api#withdraws
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch withdrawals for
     * @param {int} [limit] the maximum number of withdrawals structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    async fetchWithdrawals(code = undefined, since = undefined, limit = undefined, params = {}) {
        await this.loadMarkets();
        let currency = undefined;
        if (code !== undefined) {
            currency = this.currency(code);
        }
        const request = {};
        if (limit !== undefined) {
            request['limit'] = limit;
        }
        const response = await this.privateGetWithdraws(this.extend(request, params));
        //  {
        //   "success": true,
        //   "pagination": {
        //     "limit": 25,
        //     "order": "desc",
        //     "starting_after": null,
        //     "ending_before": null
        //   },
        //   "data": [
        //     {
        //       "id": 398,
        //       "status": "finished",
        //       "amount": "242742.0",
        //       "currency": "JPY",
        //       "created_at": "2014-12-04T15:00:00.000Z",
        //       "bank_account_id": 243,
        //       "fee": "400.0",
        //       "is_fast": true
        //     }
        //   ]
        // }
        const data = this.safeList(response, 'data', []);
        return this.parseTransactions(data, currency, since, limit, { 'type': 'withdrawal' });
    }
    parseTransactionStatus(status) {
        const statuses = {
            // withdrawals
            'pending': 'pending',
            'processing': 'pending',
            'finished': 'ok',
            'canceled': 'canceled',
            // deposits
            'confirmed': 'pending',
            'received': 'ok',
        };
        return this.safeString(statuses, status, status);
    }
    parseTransaction(transaction, currency = undefined) {
        //
        // fetchDeposits
        //
        // {
        //       "id": 2,
        //       "amount": "0.05",
        //       "currency": "BTC",
        //       "address": "**********************************",
        //       "status": "confirmed",
        //       "confirmed_at": "2015-06-13T08:29:18.000Z",
        //       "created_at": "2015-06-13T08:22:18.000Z"
        //  }
        //
        // fetchWithdrawals
        //
        //  {
        //       "id": 398,
        //       "status": "finished",
        //       "amount": "242742.0",
        //       "currency": "JPY",
        //       "created_at": "2014-12-04T15:00:00.000Z",
        //       "bank_account_id": 243,
        //       "fee": "400.0",
        //       "is_fast": true
        //  }
        //
        const id = this.safeString(transaction, 'id');
        const timestamp = this.parse8601(this.safeString(transaction, 'created_at'));
        const address = this.safeString(transaction, 'address');
        const amount = this.safeNumber(transaction, 'amount');
        const currencyId = this.safeString(transaction, 'currency');
        const code = this.safeCurrencyCode(currencyId, currency);
        const status = this.parseTransactionStatus(this.safeString(transaction, 'status'));
        const updated = this.parse8601(this.safeString(transaction, 'confirmed_at'));
        let fee = undefined;
        const feeCost = this.safeNumber(transaction, 'fee');
        if (feeCost !== undefined) {
            fee = {
                'cost': feeCost,
                'currency': code,
            };
        }
        return {
            'info': transaction,
            'id': id,
            'txid': undefined,
            'timestamp': timestamp,
            'datetime': this.iso8601(timestamp),
            'network': undefined,
            'address': address,
            'addressTo': address,
            'addressFrom': undefined,
            'tag': undefined,
            'tagTo': undefined,
            'tagFrom': undefined,
            'type': undefined,
            'amount': amount,
            'currency': code,
            'status': status,
            'updated': updated,
            'comment': undefined,
            'internal': undefined,
            'fee': fee,
        };
    }
    nonce() {
        return this.milliseconds();
    }
    sign(path, api = 'public', method = 'GET', params = {}, headers = undefined, body = undefined) {
        let url = this.urls['api']['rest'] + '/' + this.implodeParams(path, params);
        const query = this.omit(params, this.extractParams(path));
        if (api === 'public') {
            if (Object.keys(query).length) {
                url += '?' + this.urlencode(query);
            }
        }
        else {
            this.checkRequiredCredentials();
            const nonce = this.nonce().toString();
            let queryString = '';
            if (method === 'GET') {
                if (Object.keys(query).length) {
                    url += '?' + this.urlencode(this.keysort(query));
                }
            }
            else {
                if (Object.keys(query).length) {
                    body = this.urlencode(this.keysort(query));
                    queryString = body;
                }
            }
            const auth = nonce + url + queryString;
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'ACCESS-KEY': this.apiKey,
                'ACCESS-NONCE': nonce,
                'ACCESS-SIGNATURE': this.hmac(this.encode(auth), this.encode(this.secret), sha256.sha256),
            };
        }
        return { 'url': url, 'method': method, 'body': body, 'headers': headers };
    }
    handleErrors(httpCode, reason, url, method, headers, body, response, requestHeaders, requestBody) {
        if (response === undefined) {
            return undefined;
        }
        //
        //     {"success":false,"error":"disabled API Key"}'
        //     {"success":false,"error":"invalid authentication"}
        //
        const success = this.safeBool(response, 'success', true);
        if (!success) {
            const error = this.safeString(response, 'error');
            const feedback = this.id + ' ' + this.json(response);
            this.throwExactlyMatchedException(this.exceptions['exact'], error, feedback);
            this.throwBroadlyMatchedException(this.exceptions['broad'], body, feedback);
            throw new errors.ExchangeError(this.id + ' ' + this.json(response));
        }
        return undefined;
    }
}

module.exports = coincheck;
