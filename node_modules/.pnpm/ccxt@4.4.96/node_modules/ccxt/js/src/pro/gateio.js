// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code
// EDIT THE CORRESPONDENT .ts FILE INSTEAD

//  ---------------------------------------------------------------------------
import gate from './gate.js';
// ---------------------------------------------------------------------------
export default class gateio extends gate {
    describe() {
        return this.deepExtend(super.describe(), {
            'alias': true,
            'id': 'gateio',
        });
    }
}
