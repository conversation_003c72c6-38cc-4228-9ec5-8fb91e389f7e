// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code
// EDIT THE CORRESPONDENT .ts FILE INSTEAD

// ---------------------------------------------------------------------------
import coinbase from './coinbase.js';
// ---------------------------------------------------------------------------
export default class coinbaseadvanced extends coinbase {
    describe() {
        return this.deepExtend(super.describe(), {
            'id': 'coinbaseadvanced',
            'name': 'Coinbase Advanced',
            'alias': true,
        });
    }
}
