// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code
// EDIT THE CORRESPONDENT .ts FILE INSTEAD

/**
 * Asserts that the given condition is true, otherwise throws an error with an optional message.
 * @param {any} condition - The condition to check.
 * @param {string} [message] - The optional message to include in the error.
 * @throws {Error} Throws an error if the condition is false.
 */
export default function assert(condition, message) {
    if (!condition) {
        throw new Error(message || 'Assertion failure');
    }
}
