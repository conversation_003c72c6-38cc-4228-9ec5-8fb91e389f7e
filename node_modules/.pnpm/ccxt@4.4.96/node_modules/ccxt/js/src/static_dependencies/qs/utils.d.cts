export function arrayToObject(source: any, options: any): any;
export function assign(target: any, source: any): any;
export function combine(a: any, b: any): any[];
export function compact(value: any): any;
export function decode(str: any, decoder: any, charset: any): any;
export function encode(str: any, defaultEncoder: any, charset: any): any;
export function isBuffer(obj: any): boolean;
export function isRegExp(obj: any): boolean;
export function merge(target: any, source: any, options: any): any;
