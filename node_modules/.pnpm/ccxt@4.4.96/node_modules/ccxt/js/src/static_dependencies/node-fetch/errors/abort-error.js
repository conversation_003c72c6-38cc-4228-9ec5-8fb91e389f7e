// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code
// EDIT THE CORRESPONDENT .ts FILE INSTEAD

import { FetchBaseError } from './base.js';
/**
 * AbortError interface for cancelled requests
 */
export class AbortError extends FetchBaseError {
    constructor(message, type = 'aborted') {
        super(message, type);
    }
}
