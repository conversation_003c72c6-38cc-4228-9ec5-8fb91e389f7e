{"name": "ccxt", "version": "4.4.96", "description": "A JavaScript / TypeScript / Python / C# / PHP cryptocurrency trading library with support for 100+ exchanges", "unpkg": "dist/ccxt.browser.min.js", "type": "module", "exports": {".": {"import": "./js/ccxt.js", "require": "./dist/ccxt.cjs"}}, "engines": {"node": ">=15.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.com"}, "repository": {"type": "git", "url": "git+https://github.com/ccxt/ccxt.git"}, "readme": "README.md", "scripts": {"instrument": "nyc instrument js/ jsInstrumented/", "nyc-coverage": "nyc --reporter=html --reporter=lcov --exclude='js/src/pro/**' --exclude='js/src/base/**' --exclude='js/src/test/**' --exclude='js/src/abstract/**' --exclude='js/src/static_dependencies' node jsInstrumented/src/test/tests.init.js --requestTests --responseTests", "coverage-js": "npm run instrument && npm run nyc-coverage && rm -rf jsInstrumented", "docker": "docker-compose run --rm ccxt", "fixTSBug": "node build/fixTSBug", "transpileCsSingle": "tsx build/csharpTranspiler.ts", "transpileCS": "tsx build/csharpTranspiler.ts --multi", "transpileCSWs": "tsx build/csharpTranspiler.ts --ws", "buildCS": "dotnet build cs/ccxt.sln", "buildGO": "go build -C go ./v4", "transpileGO": "tsx build/goTranspiler.ts", "buildCSRelease": "dotnet build cs --configuration Release", "csharp": "npm run transpileCS && npm run transpileCSWs && npm run buildCS", "go": "npm run transpileGO && npm run buildGO", "force-build//WithoutGo": "npm run pre-transpile && npm run force-transpile-fast && npm run csharp && npm run post-transpile && npm run update-badges", "force-build": "npm run pre-transpile && npm run go && npm run force-transpile-fast && npm run csharp && npm run post-transpile && npm run update-badges", "//TMPCommentforce-build": "npm run pre-transpile && npm run force-transpile-fast && npm run csharp && npm run go && npm run post-transpile && npm run update-badges", "build-docs": "node jsdoc2md.js && node examples2md.js", "serve-docs": "docsify serve ./wiki", "tsBuildFile": "tsc --skipLibCheck --strictNullChecks false --strict --noImplicitAny false --esModuleInterop --isolatedModules false --forceConsistentCasingInFileNames --removeComments false --target ES2020 --declaration --allowJs --checkJs false --moduleResolution Node --module ES2022 --outDir ./js/src --lib ES2020.BigInt --lib dom ", "addJsHeaders": "tsx build/transpile.ts --js-headers", "tsBuild": "tsc && npm run addJsHeaders || echo \"\"", "tsBuildExamples": "tsc -p ./examples/tsconfig.json", "emitAPI": "tsx build/generateImplicitAPI.ts", "emitAPITs": "tsx build/generateImplicitAPI.ts -- --ts", "emitAPIPy": "tsx build/generateImplicitAPI.ts -- --python", "emitAPIPhp": "tsx build/generateImplicitAPI.ts -- --php", "emitAPIGo": "tsx build/generateImplicitAPI.ts -- --go", "emitAPICs": "tsx build/generateImplicitAPI.ts -- --csharp", "build": "npm run pre-transpile && npm run transpile && npm run post-transpile && npm run update-badges && npm run build-docs", "force-build-slow": "npm run pre-transpile && npm run force-transpile && npm run post-transpile && npm run update-badges", "pre-transpile-js": "npm run export-exchanges && npm run vss && npm run emitAPITs && npm run tsBuild && npm run validate-types && npm run tsBuildExamples && npm run check-js-syntax && npm run bundle", "pre-transpile-py": "npm run export-exchanges && npm run emitAPIPy", "pre-transpile-php": "npm run export-exchanges && npm run emitAPIPhp", "pre-transpile-go": "npm run export-exchanges && npm run emitAPIGo", "pre-transpile-js-simple": "npm run export-exchanges && npm run emitAPITs && tsc && npm run validate-types", "pre-transpile-cs": "npm run export-exchanges && npm run emitAPICs", "pre-transpile": "npm run export-exchanges && npm run vss && npm run tsBuild && npm run emitAPI && npm run validate-types && npm run tsBuildExamples && npm run copy-python-files && npm run check-js-syntax && npm run bundle", "pre-transpile-pr": "npm run export-exchanges && npm run tsBuild && npm run emitAPI && npm run check-js-syntax", "post-transpile": "npm run check-python-syntax && npm run check-php-syntax", "cli.ts": "tsx ./cli/ts/cli.ts", "cli.js": "node ./cli/js/cli.js", "cli.py": "python3 ./examples/py/cli.py", "cli.php": "php ./examples/php/cli.php", "cli.cs": "dotnet run --project \"./cs/cli/cli.csproj\"", "cli.go": "go run ./go/cli/main.go", "export-exchanges": "node build/export-exchanges", "capabilities": "node ./examples/js/exchange-capabilities.js", "git-ignore-generated-files": "node build/git-ignore-generated-files.cjs", "git-unignore-generated-files": "node build/git-ignore-generated-files.cjs --unignore", "update-badges": "node build/update-badges", "update-links": "node build/update-links", "transpile": "npm run transpileRest && npm run transpileWs", "transpileRest": "tsx build/transpile.ts", "transpileWs": "tsx build/transpileWS.ts", "force-transpile": "npm run force-transpileRest && npm run force-transpileWs", "force-transpile-fast": "npm run dev-force-transpile", "force-transpile-fast-py": "npm run fast-force-transpileRest-py && npm run fast-force-transpileWs-py", "force-transpile-fast-php": "npm run fast-force-transpileRest-php && npm run fast-force-transpileWs-php", "dev-force-transpile": "npm run fast-force-transpileRest && npm run fast-force-transpileWs", "force-transpileRest": "tsx build/transpile.ts --force", "fast-force-transpileRest-py": "tsx build/transpile.ts --multiprocess --force --python", "fast-force-transpileRest-php": "tsx build/transpile.ts --multiprocess --force --php", "fast-force-transpileRest": "tsx build/transpile.ts --multiprocess --force", "force-transpileWs": "tsx build/transpileWS.ts --force", "fast-force-transpileWs": "tsx build/transpileWS.ts --multiprocess --force", "fast-force-transpileWs-py": "tsx build/transpileWS.ts --multiprocess --force --python", "fast-force-transpileWs-php": "tsx build/transpileWS.ts --multiprocess --force --php", "vss": "node build/vss", "lint": "eslint \"ts/src/*.ts\" \"ts/src/base/Exchange.ts\" \"ts/src/pro/*.ts\" --cache --cache-location .cache/eslintcache --cache-strategy metadata", "check-syntax": "npm run transpile && npm run check-js-syntax && npm run check-python-syntax && npm run check-php-syntax", "check-js-syntax": "node -e \"console.log(process.cwd())\" && eslint --version && npm run lint", "eslint": "eslint", "check-python-syntax": "cd python && tox -e qa && cd ..", "check-python-types": "cd python && tox -e type && cd ..", "check-php-syntax": "npm run check-rest-php-syntax && npm run check-ws-php-syntax", "check-rest-php-syntax": "php -f php/test/custom/syntax.php", "check-ws-php-syntax": "php -f php/pro/test/custom/syntax.php", "bundle": "npm run bundle-cjs && npm run bundle-browser", "bundle-cjs": "rollup -c rollup.config.js", "bundle-browser": "webpack build -c webpack.config.js && webpack build -c webpack.config.js --optimization-minimize --output-filename ccxt.browser.min.js", "copy-python-files": "npm run copy-python-package && npm run copy-python-license && npm run copy-python-keys && npm run copy-python-readme", "copy-python-package": "node build/copy package.json python/package.json", "copy-python-license": "node build/copy LICENSE.txt python/LICENSE.txt", "copy-python-keys": "node build/copy keys.json python/keys.json", "copy-python-readme": "node build/copy README.md python/README.md", "postinstall": "node postinstall.js", "validate-types": "tsx build/validate-types.ts", "runtests": "node run-tests", "live-tests": "node run-tests --useProxy", "live-tests-rest": "npm run live-tests", "live-tests-ws": "npm run live-tests -- --ws", "live-tests-rest-ts": "npm run live-tests -- --ts", "live-tests-ws-ts": "npm run live-tests -- --ts     --ws", "live-tests-rest-js": "npm run live-tests -- --js", "live-tests-ws-js": "npm run live-tests -- --js     --ws", "live-tests-rest-py": "npm run live-tests -- --python", "live-tests-ws-py": "npm run live-tests -- --python --ws", "live-tests-rest-php": "npm run live-tests -- --php", "live-tests-ws-php": "npm run live-tests -- --php    --ws", "live-tests-rest-csharp": "npm run live-tests -- --csharp", "live-tests-rest-go": "npm run live-tests -- --go", "live-tests-ws-csharp": "npm run live-tests -- --csharp --ws", "ti-ts": "tsx ts/src/test/tests.init.ts", "ti-js": "node js/src/test/tests.init.js", "ti-py": "python3 python/ccxt/test/tests_init.py", "ti-php": "php php/test/tests_init.php", "ti-cs": "dotnet run --project cs/tests/tests.csproj", "ti-go": "go run -C go ./tests/main.go", "request-ts": "npm run ti-ts  -- --requestTests", "request-js": "npm run ti-js  -- --requestTests", "request-py-async": "npm run ti-py  -- --requestTests", "request-py-sync": "npm run ti-py  -- --requestTests --sync", "request-py": "npm run request-py-sync && npm run request-py-async", "request-php-async": "npm run ti-php -- --requestTests", "request-php-sync": "npm run ti-php -- --requestTests --sync", "request-php": "npm run request-php-sync && npm run request-php-async", "request-cs": "npm run ti-cs  -- --requestTests", "request-go": "npm run ti-go -- --requestTests", "request-tests//withoutGo": "npm run request-js && npm run request-py && npm run request-php && npm run request-cs", "request-tests": "npm run request-js && npm run request-py && npm run request-php && npm run request-cs && npm run request-go", "response-ts": "npm run ti-ts  -- --responseTests", "response-js": "npm run ti-js  -- --responseTests", "response-py-sync": "npm run ti-py  -- --responseTests --sync", "response-py-async": "npm run ti-py  -- --responseTests", "response-py": "npm run response-py-sync && npm run response-py-async", "response-cs": "npm run ti-cs  -- --responseTests", "response-go": "npm run ti-go  -- --responseTests", "response-php-async": "npm run ti-php -- --responseTests", "response-php-sync": "npm run ti-php -- --responseTests --sync", "response-php": "npm run response-php-sync && npm run response-php-async", "response-tests": "npm run response-js && npm run response-py && npm run response-php && npm run response-cs && npm run response-go", "response-tests//withoutGo": "npm run response-js && npm run response-py && npm run response-php && npm run response-cs", "static-updater": "tsx ./utils/update-static-json --update", "id-tests-js": "npm run ti-js  -- --idTests", "id-tests-py": "npm run ti-py  -- --idTests", "id-tests-php": "npm run ti-php -- --idTests", "id-tests-cs": "npm run ti-cs  -- --idTests", "id-tests-go": "npm run ti-go  -- --idTests", "id-tests": "npm run id-tests-js && npm run id-tests-py && npm run id-tests-php && npm run id-tests-cs", "id-tests//withGO": "npm run id-tests-js && npm run id-tests-py && npm run id-tests-php && npm run id-tests-cs && npm run id-tests-go", "test-base-rest-ts": "npm run ti-ts  -- --baseTests", "test-base-rest-js": "npm run ti-js  -- --baseTests", "test-base-rest-py": "npm run ti-py  -- --baseTests", "test-base-rest-php": "npm run ti-php -- --baseTests", "test-base-rest-cs": "npm run ti-cs  -- --baseTests", "test-base-rest-go": "npm run ti-go  -- --baseTests && cd ../", "test-base-ws-ts": "npm run ti-ts  -- --baseTests --ws", "test-base-ws-js": "npm run ti-js  -- --baseTests --ws", "test-base-ws-py": "npm run ti-py  -- --baseTests --ws", "test-base-ws-php": "npm run ti-php -- --baseTests --ws", "test-base-ws-cs": "npm run ti-cs  -- --baseTests --ws", "test-base-rest": "npm run test-base-rest-js && npm run test-base-rest-py && npm run test-base-rest-php && npm run test-base-rest-cs", "test-base-ws": "npm run test-base-ws-js   && npm run test-base-ws-py   && npm run test-base-ws-php   && npm run test-base-ws-cs", "test": "npm run build && npm run commonjs-test && npm run id-tests && npm run request-tests && npm run response-tests && npm run live-tests", "commonjs-test": "node ./utils/test-commonjs.cjs", "package-test": "./utils/package-test.sh", "test-freshness": "tsx ./utils/test-freshness.ts", "benchmark": "tsx examples/ts/benchmark.ts", "cleanup-old-tags": "tsx ./build/cleanup-old-tags.ts", "test-types-go": "go run -C go ./tests/types/types.go"}, "types": "./js/ccxt.d.ts", "devDependencies": {"@rollup/plugin-commonjs": "^21.0.3", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@types/node": "^18.15.11", "@typescript-eslint/eslint-plugin": "^5.30.5", "@typescript-eslint/parser": "^5.30.5", "ansicolor": "^2.0.0", "as-table": "^1.0.55", "asciichart": "^1.5.25", "assert": "^2.0.0", "ast-transpiler": "^0.0.66", "docsify": "^4.11.4", "eslint": "8.22.0", "eslint-config-airbnb-base": "15.0.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-jsdoc": "^46.9.0", "esmify": "^2.1.1", "https-proxy-agent": "^5.0.1", "jsdoc-to-markdown": "^8.0.0", "ololog": "^1.1.175", "piscina": "^3.2.0", "replace-in-file": "^6.3.5", "rollup": "^2.70.1", "rollup-plugin-execute": "1.1.1", "terser-webpack-plugin": "^5.3.9", "ts-loader": "^9.4.2", "tsx": "^4.7.2", "typescript": "4.7.4", "webpack": "^5.76.2", "webpack-cli": "^5.0.1"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kroitor"}, "license": "MIT", "bugs": {"url": "https://github.com/ccxt/ccxt/issues"}, "homepage": "https://ccxt.com", "keywords": ["algorithmic", "algotrading", "altcoin", "altcoins", "api", "arbitrage", "real-time", "realtime", "backtest", "backtesting", "bitcoin", "bot", "btc", "cny", "coin", "coins", "crypto", "cryptocurrency", "crypto currency", "crypto market", "currency", "currencies", "darkcoin", "dash", "digital currency", "doge", "<PERSON><PERSON><PERSON><PERSON>", "e-commerce", "etc", "eth", "ether", "ethereum", "exchange", "exchanges", "eur", "framework", "invest", "investing", "investor", "library", "light", "litecoin", "ltc", "market", "market data", "markets", "merchandise", "merchant", "minimal", "ohlcv", "order", "orderbook", "order book", "price", "price data", "pricefeed", "private", "public", "ripple", "strategy", "ticker", "tickers", "toolkit", "trade", "trader", "trading", "usd", "volume", "websocket", "websockets", "web socket", "web sockets", "ws", "xbt", "xrp", "zec", "zerocoin"], "collective": {"type": "opencollective", "url": "https://opencollective.com/ccxt", "logo": "https://opencollective.com/ccxt/logo.txt"}, "ethereum": "******************************************", "dependencies": {"ws": "^8.8.1"}}