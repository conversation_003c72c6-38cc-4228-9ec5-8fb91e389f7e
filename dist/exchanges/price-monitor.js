import ccxt from 'ccxt';
// ccxt.pro 是 ccxt 的一部分
const ccxtpro = ccxt.pro;
import { EventEmitter } from 'events';
import { logger } from '../utils/logger.js';
import { delay } from '../utils/index.js';
/**
 * WebSocket 价格监控器
 */
export class PriceMonitor extends EventEmitter {
    exchangeConfigs;
    symbols;
    reconnectDelay;
    exchanges = new Map();
    priceData = new Map(); // symbol -> exchange -> price
    connections = new Map();
    reconnectTimers = new Map();
    isRunning = false;
    constructor(exchangeConfigs, symbols, reconnectDelay = 5000) {
        super();
        this.exchangeConfigs = exchangeConfigs;
        this.symbols = symbols;
        this.reconnectDelay = reconnectDelay;
        this.initializeExchanges();
    }
    /**
     * 初始化交易所实例
     */
    initializeExchanges() {
        for (const config of this.exchangeConfigs) {
            try {
                const ExchangeClass = ccxtpro[config.id];
                if (!ExchangeClass) {
                    logger.error(`不支持的交易所: ${config.id}`);
                    continue;
                }
                const exchange = new ExchangeClass({
                    apiKey: config.apiKey,
                    secret: config.secret,
                    password: config.password,
                    sandbox: config.sandbox,
                    enableRateLimit: config.enableRateLimit,
                    rateLimit: config.rateLimit,
                });
                this.exchanges.set(config.id, exchange);
                this.connections.set(config.id, false);
                // 初始化价格数据存储
                for (const symbol of this.symbols) {
                    if (!this.priceData.has(symbol)) {
                        this.priceData.set(symbol, new Map());
                    }
                }
                logger.info(`已初始化交易所: ${config.name}`, undefined, { exchange: config.id });
            }
            catch (error) {
                logger.error(`初始化交易所失败: ${config.name}`, error, { exchange: config.id });
            }
        }
    }
    /**
     * 启动价格监控
     */
    async start() {
        if (this.isRunning) {
            logger.warn('价格监控器已在运行中');
            return;
        }
        this.isRunning = true;
        logger.info('启动价格监控器');
        // 为每个交易所启动监控
        const promises = Array.from(this.exchanges.entries()).map(([exchangeId, exchange]) => this.startExchangeMonitoring(exchangeId, exchange));
        await Promise.allSettled(promises);
    }
    /**
     * 停止价格监控
     */
    async stop() {
        if (!this.isRunning) {
            return;
        }
        this.isRunning = false;
        logger.info('停止价格监控器');
        // 清理重连定时器
        for (const timer of this.reconnectTimers.values()) {
            clearTimeout(timer);
        }
        this.reconnectTimers.clear();
        // 关闭所有交易所连接
        const promises = Array.from(this.exchanges.values()).map(exchange => exchange.close().catch((error) => logger.error('关闭交易所连接失败', error)));
        await Promise.allSettled(promises);
        this.connections.clear();
    }
    /**
     * 启动单个交易所的监控
     */
    async startExchangeMonitoring(exchangeId, exchange) {
        logger.info(`启动交易所监控: ${exchangeId}`, undefined, { exchange: exchangeId });
        try {
            // 检查交易所是否支持 WebSocket
            if (!exchange.has['watchTickers'] && !exchange.has['watchTicker']) {
                logger.warn(`交易所不支持 WebSocket ticker: ${exchangeId}`, undefined, { exchange: exchangeId });
                return;
            }
            // 加载市场数据
            await exchange.loadMarkets();
            this.connections.set(exchangeId, true);
            this.emit('websocket_event', {
                type: 'connection_status',
                data: { exchange: exchangeId, connected: true }
            });
            // 监控所有交易对
            if (exchange.has['watchTickers']) {
                // 批量监控
                this.watchTickers(exchangeId, exchange);
            }
            else {
                // 单独监控每个交易对
                for (const symbol of this.symbols) {
                    this.watchTicker(exchangeId, exchange, symbol);
                }
            }
        }
        catch (error) {
            logger.error(`启动交易所监控失败: ${exchangeId}`, error, { exchange: exchangeId });
            this.handleConnectionError(exchangeId, exchange);
        }
    }
    /**
     * 监控多个 ticker
     */
    async watchTickers(exchangeId, exchange) {
        while (this.isRunning && this.connections.get(exchangeId)) {
            try {
                const tickers = await exchange.watchTickers(this.symbols);
                for (const [symbol, ticker] of Object.entries(tickers)) {
                    const tickerData = ticker;
                    if (this.symbols.includes(symbol) && tickerData.bid && tickerData.ask) {
                        this.updatePriceData(exchangeId, symbol, tickerData);
                    }
                }
            }
            catch (error) {
                logger.error(`监控 tickers 失败: ${exchangeId}`, error, { exchange: exchangeId });
                this.handleConnectionError(exchangeId, exchange);
                break;
            }
        }
    }
    /**
     * 监控单个 ticker
     */
    async watchTicker(exchangeId, exchange, symbol) {
        while (this.isRunning && this.connections.get(exchangeId)) {
            try {
                const ticker = await exchange.watchTicker(symbol);
                if (ticker.bid && ticker.ask) {
                    this.updatePriceData(exchangeId, symbol, ticker);
                }
            }
            catch (error) {
                logger.error(`监控 ticker 失败: ${exchangeId} ${symbol}`, error, {
                    exchange: exchangeId,
                    symbol
                });
                // 单个交易对失败不影响其他交易对
                await delay(1000);
            }
        }
    }
    /**
     * 更新价格数据
     */
    updatePriceData(exchangeId, symbol, ticker) {
        const priceData = {
            exchange: exchangeId,
            symbol,
            bid: ticker.bid || 0,
            ask: ticker.ask || 0,
            timestamp: ticker.timestamp || Date.now(),
            datetime: ticker.datetime || new Date().toISOString()
        };
        // 存储价格数据
        const symbolData = this.priceData.get(symbol);
        if (symbolData) {
            symbolData.set(exchangeId, priceData);
        }
        // 发出价格更新事件
        this.emit('websocket_event', {
            type: 'price_update',
            data: priceData
        });
        logger.debug(`价格更新: ${symbol} @ ${exchangeId}`, {
            bid: priceData.bid,
            ask: priceData.ask
        }, { exchange: exchangeId, symbol });
    }
    /**
     * 处理连接错误
     */
    handleConnectionError(exchangeId, exchange) {
        this.connections.set(exchangeId, false);
        this.emit('websocket_event', {
            type: 'connection_status',
            data: { exchange: exchangeId, connected: false }
        });
        // 设置重连定时器
        if (this.isRunning && !this.reconnectTimers.has(exchangeId)) {
            const timer = setTimeout(() => {
                this.reconnectTimers.delete(exchangeId);
                if (this.isRunning) {
                    logger.info(`尝试重连交易所: ${exchangeId}`, undefined, { exchange: exchangeId });
                    this.startExchangeMonitoring(exchangeId, exchange);
                }
            }, this.reconnectDelay);
            this.reconnectTimers.set(exchangeId, timer);
        }
    }
    /**
     * 获取市场快照
     */
    getMarketSnapshot(symbol) {
        const symbolData = this.priceData.get(symbol);
        if (!symbolData || symbolData.size === 0) {
            return null;
        }
        return {
            symbol,
            exchanges: new Map(symbolData),
            lastUpdate: Math.max(...Array.from(symbolData.values()).map(data => data.timestamp))
        };
    }
    /**
     * 获取所有市场快照
     */
    getAllMarketSnapshots() {
        const snapshots = new Map();
        for (const symbol of this.symbols) {
            const snapshot = this.getMarketSnapshot(symbol);
            if (snapshot) {
                snapshots.set(symbol, snapshot);
            }
        }
        return snapshots;
    }
    /**
     * 获取连接状态
     */
    getConnectionStatus() {
        return new Map(this.connections);
    }
    /**
     * 检查是否正在运行
     */
    isMonitoring() {
        return this.isRunning;
    }
}
