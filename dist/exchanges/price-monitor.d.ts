import { EventEmitter } from 'events';
import { ExchangeConfig, MarketSnapshot } from '../types/index.js';
/**
 * WebSocket 价格监控器
 */
export declare class PriceMonitor extends EventEmitter {
    private exchangeConfigs;
    private symbols;
    private reconnectDelay;
    private exchanges;
    private priceData;
    private connections;
    private reconnectTimers;
    private isRunning;
    constructor(exchangeConfigs: ExchangeConfig[], symbols: string[], reconnectDelay?: number);
    /**
     * 初始化交易所实例
     */
    private initializeExchanges;
    /**
     * 启动价格监控
     */
    start(): Promise<void>;
    /**
     * 停止价格监控
     */
    stop(): Promise<void>;
    /**
     * 启动单个交易所的监控
     */
    private startExchangeMonitoring;
    /**
     * 监控多个 ticker
     */
    private watchTickers;
    /**
     * 监控单个 ticker
     */
    private watchTicker;
    /**
     * 更新价格数据
     */
    private updatePriceData;
    /**
     * 处理连接错误
     */
    private handleConnectionError;
    /**
     * 获取市场快照
     */
    getMarketSnapshot(symbol: string): MarketSnapshot | null;
    /**
     * 获取所有市场快照
     */
    getAllMarketSnapshots(): Map<string, MarketSnapshot>;
    /**
     * 获取连接状态
     */
    getConnectionStatus(): Map<string, boolean>;
    /**
     * 检查是否正在运行
     */
    isMonitoring(): boolean;
}
