/**
 * 生成唯一 ID
 */
export declare function generateId(): string;
/**
 * 计算价差百分比
 */
export declare function calculateSpreadPercentage(buyPrice: number, sellPrice: number): number;
/**
 * 计算净利润
 */
export declare function calculateNetProfit(buyPrice: number, sellPrice: number, amount: number, buyFee: number, sellFee: number): number;
/**
 * 计算净利润百分比
 */
export declare function calculateNetProfitPercentage(buyPrice: number, sellPrice: number, buyFee: number, sellFee: number): number;
/**
 * 格式化数字为指定小数位
 */
export declare function formatNumber(num: number, decimals?: number): number;
/**
 * 格式化百分比
 */
export declare function formatPercentage(num: number, decimals?: number): string;
/**
 * 格式化货币
 */
export declare function formatCurrency(num: number, currency?: string, decimals?: number): string;
/**
 * 延迟函数
 */
export declare function delay(ms: number): Promise<void>;
/**
 * 重试函数
 */
export declare function retry<T>(fn: () => Promise<T>, maxAttempts?: number, delayMs?: number): Promise<T>;
/**
 * 安全的 JSON 解析
 */
export declare function safeJsonParse<T>(json: string, defaultValue: T): T;
/**
 * 检查对象是否为空
 */
export declare function isEmpty(obj: any): boolean;
/**
 * 深度克隆对象
 */
export declare function deepClone<T>(obj: T): T;
/**
 * 获取时间戳
 */
export declare function getTimestamp(): number;
/**
 * 格式化时间
 */
export declare function formatTime(timestamp: number): string;
/**
 * 计算执行时间
 */
export declare function measureExecutionTime<T>(fn: () => T): {
    result: T;
    duration: number;
};
/**
 * 异步计算执行时间
 */
export declare function measureAsyncExecutionTime<T>(fn: () => Promise<T>): Promise<{
    result: T;
    duration: number;
}>;
/**
 * 限制并发数
 */
export declare class ConcurrencyLimiter {
    private limit;
    private running;
    private queue;
    constructor(limit: number);
    execute<T>(fn: () => Promise<T>): Promise<T>;
    private processQueue;
}
