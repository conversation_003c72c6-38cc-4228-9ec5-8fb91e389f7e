import { EventEmitter } from 'events';
/**
 * 性能监控器
 */
export declare class PerformanceMonitor extends EventEmitter {
    private reportInterval;
    private metrics;
    private timers;
    private counters;
    private isMonitoring;
    private monitoringInterval?;
    constructor(reportInterval?: number);
    /**
     * 启动性能监控
     */
    start(): void;
    /**
     * 停止性能监控
     */
    stop(): void;
    /**
     * 开始计时
     */
    startTimer(name: string): void;
    /**
     * 结束计时并记录
     */
    endTimer(name: string): number;
    /**
     * 记录指标
     */
    recordMetric(name: string, value: number): void;
    /**
     * 增加计数器
     */
    incrementCounter(name: string, value?: number): void;
    /**
     * 重置计数器
     */
    resetCounter(name: string): void;
    /**
     * 获取指标统计
     */
    getMetricStats(name: string): {
        count: number;
        min: number;
        max: number;
        avg: number;
        latest: number;
    } | null;
    /**
     * 获取计数器值
     */
    getCounter(name: string): number;
    /**
     * 获取所有指标
     */
    getAllMetrics(): Map<string, ReturnType<PerformanceMonitor['getMetricStats']>>;
    /**
     * 获取所有计数器
     */
    getAllCounters(): Map<string, number>;
    /**
     * 报告性能指标
     */
    private reportMetrics;
    /**
     * 启动系统监控
     */
    private startSystemMonitoring;
    /**
     * 获取系统指标
     */
    private getSystemMetrics;
    /**
     * 清理指标数据
     */
    clearMetrics(): void;
}
export declare const performanceMonitor: PerformanceMonitor;
