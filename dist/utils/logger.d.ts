import { LogLevel, LogEntry } from '../types/index.js';
/**
 * 日志器类
 */
export declare class Logger {
    private logs;
    private maxLogs;
    /**
     * 格式化时间戳
     */
    private formatTimestamp;
    /**
     * 格式化日志消息
     */
    private formatMessage;
    /**
     * 记录日志
     */
    private log;
    /**
     * Debug 级别日志
     */
    debug(message: string, data?: any, context?: {
        exchange?: string;
        symbol?: string;
        opportunityId?: string;
        tradeId?: string;
    }): void;
    /**
     * Info 级别日志
     */
    info(message: string, data?: any, context?: {
        exchange?: string;
        symbol?: string;
        opportunityId?: string;
        tradeId?: string;
    }): void;
    /**
     * Warn 级别日志
     */
    warn(message: string, data?: any, context?: {
        exchange?: string;
        symbol?: string;
        opportunityId?: string;
        tradeId?: string;
    }): void;
    /**
     * Error 级别日志
     */
    error(message: string, data?: any, context?: {
        exchange?: string;
        symbol?: string;
        opportunityId?: string;
        tradeId?: string;
    }): void;
    /**
     * 获取日志历史
     */
    getLogs(level?: LogLevel, limit?: number): LogEntry[];
    /**
     * 清空日志
     */
    clear(): void;
    /**
     * 获取统计信息
     */
    getStats(): Record<LogLevel, number>;
}
export declare const logger: Logger;
