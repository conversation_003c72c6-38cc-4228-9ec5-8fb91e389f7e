import crypto from 'crypto';
/**
 * 生成唯一 ID
 */
export function generateId() {
    return crypto.randomUUID();
}
/**
 * 计算价差百分比
 */
export function calculateSpreadPercentage(buyPrice, sellPrice) {
    if (buyPrice <= 0 || sellPrice <= 0)
        return 0;
    return ((sellPrice - buyPrice) / buyPrice) * 100;
}
/**
 * 计算净利润
 */
export function calculateNetProfit(buyPrice, sellPrice, amount, buyFee, sellFee) {
    const grossProfit = (sellPrice - buyPrice) * amount;
    const totalFees = (buyPrice * amount * buyFee) + (sellPrice * amount * sellFee);
    return grossProfit - totalFees;
}
/**
 * 计算净利润百分比
 */
export function calculateNetProfitPercentage(buyPrice, sellPrice, buyFee, sellFee) {
    if (buyPrice <= 0)
        return 0;
    const grossProfitPercentage = ((sellPrice - buyPrice) / buyPrice) * 100;
    const totalFeePercentage = (buyFee + sellFee) * 100;
    return grossProfitPercentage - totalFeePercentage;
}
/**
 * 格式化数字为指定小数位
 */
export function formatNumber(num, decimals = 8) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
}
/**
 * 格式化百分比
 */
export function formatPercentage(num, decimals = 4) {
    return `${formatNumber(num, decimals)}%`;
}
/**
 * 格式化货币
 */
export function formatCurrency(num, currency = 'USDT', decimals = 2) {
    return `${formatNumber(num, decimals)} ${currency}`;
}
/**
 * 延迟函数
 */
export function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
/**
 * 重试函数
 */
export async function retry(fn, maxAttempts = 3, delayMs = 1000) {
    let lastError;
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await fn();
        }
        catch (error) {
            lastError = error;
            if (attempt === maxAttempts) {
                throw lastError;
            }
            await delay(delayMs * attempt);
        }
    }
    throw lastError;
}
/**
 * 安全的 JSON 解析
 */
export function safeJsonParse(json, defaultValue) {
    try {
        return JSON.parse(json);
    }
    catch {
        return defaultValue;
    }
}
/**
 * 检查对象是否为空
 */
export function isEmpty(obj) {
    if (obj == null)
        return true;
    if (Array.isArray(obj))
        return obj.length === 0;
    if (typeof obj === 'object')
        return Object.keys(obj).length === 0;
    return false;
}
/**
 * 深度克隆对象
 */
export function deepClone(obj) {
    if (obj === null || typeof obj !== 'object')
        return obj;
    if (obj instanceof Date)
        return new Date(obj.getTime());
    if (obj instanceof Array)
        return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
    return obj;
}
/**
 * 获取时间戳
 */
export function getTimestamp() {
    return Date.now();
}
/**
 * 格式化时间
 */
export function formatTime(timestamp) {
    return new Date(timestamp).toISOString();
}
/**
 * 计算执行时间
 */
export function measureExecutionTime(fn) {
    const start = performance.now();
    const result = fn();
    const duration = performance.now() - start;
    return { result, duration };
}
/**
 * 异步计算执行时间
 */
export async function measureAsyncExecutionTime(fn) {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    return { result, duration };
}
/**
 * 限制并发数
 */
export class ConcurrencyLimiter {
    limit;
    running = 0;
    queue = [];
    constructor(limit) {
        this.limit = limit;
    }
    async execute(fn) {
        return new Promise((resolve, reject) => {
            const task = async () => {
                this.running++;
                try {
                    const result = await fn();
                    resolve(result);
                }
                catch (error) {
                    reject(error);
                }
                finally {
                    this.running--;
                    this.processQueue();
                }
            };
            if (this.running < this.limit) {
                task();
            }
            else {
                this.queue.push(task);
            }
        });
    }
    processQueue() {
        if (this.queue.length > 0 && this.running < this.limit) {
            const task = this.queue.shift();
            task();
        }
    }
}
