#!/usr/bin/env node
import { ArbitrageEngine } from './arbitrage/arbitrage-engine.js';
import { loadConfig, validateConfig } from './config/index.js';
import { logger } from './utils/logger.js';
import { formatCurrency, formatPercentage } from './utils/index.js';
/**
 * 主应用类
 */
class ArbitrageApp {
    engine;
    isShuttingDown = false;
    /**
     * 启动应用
     */
    async start() {
        try {
            logger.info('🚀 启动跨所套利程序');
            // 加载配置
            const config = loadConfig();
            logger.info('📋 配置加载完成', {
                exchanges: config.exchanges.map(e => e.name),
                symbols: config.symbols,
                tradingEnabled: config.trading.enabled,
                dryRun: config.trading.dryRun
            });
            // 验证配置
            const errors = validateConfig(config);
            if (errors.length > 0) {
                logger.error('❌ 配置验证失败', { errors });
                process.exit(1);
            }
            // 创建套利引擎
            this.engine = new ArbitrageEngine(config);
            this.setupEventHandlers();
            // 启动引擎
            await this.engine.start();
            // 显示状态信息
            this.displayStatus();
            this.startStatusUpdates();
            logger.info('✅ 套利程序启动成功');
        }
        catch (error) {
            logger.error('❌ 启动失败', error);
            process.exit(1);
        }
    }
    /**
     * 设置事件处理器
     */
    setupEventHandlers() {
        if (!this.engine)
            return;
        this.engine.on('websocket_event', (event) => {
            switch (event.type) {
                case 'opportunity_detected':
                    const opportunity = event.data;
                    logger.info(`💰 发现套利机会`, {
                        symbol: opportunity.symbol,
                        buyExchange: opportunity.buyExchange,
                        sellExchange: opportunity.sellExchange,
                        profit: formatCurrency(opportunity.netProfit),
                        profitPercentage: formatPercentage(opportunity.netProfitPercentage)
                    }, {
                        symbol: opportunity.symbol,
                        opportunityId: opportunity.id
                    });
                    break;
                case 'trade_executed':
                    const trade = event.data;
                    logger.info(`🔄 交易执行`, {
                        symbol: trade.symbol,
                        status: trade.status,
                        tradeId: trade.id
                    }, {
                        symbol: trade.symbol,
                        tradeId: trade.id
                    });
                    break;
                case 'connection_status':
                    const { exchange, connected } = event.data;
                    if (connected) {
                        logger.info(`🔗 ${exchange} 连接成功`, undefined, { exchange });
                    }
                    else {
                        logger.warn(`⚠️  ${exchange} 连接断开`, undefined, { exchange });
                    }
                    break;
                case 'error':
                    const { exchange: errorExchange, error } = event.data;
                    logger.error(`❌ ${errorExchange} 发生错误`, { error }, { exchange: errorExchange });
                    break;
            }
        });
        // 处理进程信号
        process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));
        process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
        process.on('uncaughtException', (error) => {
            logger.error('未捕获的异常', error);
            this.gracefulShutdown('uncaughtException');
        });
        process.on('unhandledRejection', (reason) => {
            logger.error('未处理的 Promise 拒绝', { reason });
            this.gracefulShutdown('unhandledRejection');
        });
    }
    /**
     * 显示状态信息
     */
    displayStatus() {
        if (!this.engine)
            return;
        const status = this.engine.getStatus();
        console.log('\n📊 当前状态:');
        console.log(`   运行状态: ${status.isRunning ? '✅ 运行中' : '❌ 已停止'}`);
        console.log('\n🔗 交易所连接:');
        for (const [exchange, connected] of status.connections) {
            console.log(`   ${exchange}: ${connected ? '✅ 已连接' : '❌ 断开'}`);
        }
        console.log('\n💹 活跃交易:');
        if (status.activeTrades.length === 0) {
            console.log('   无活跃交易');
        }
        else {
            for (const trade of status.activeTrades) {
                console.log(`   ${trade.symbol}: ${trade.status} (${trade.id.slice(0, 8)})`);
            }
        }
        console.log('\n🎯 最佳机会:');
        if (status.bestOpportunities.length === 0) {
            console.log('   暂无套利机会');
        }
        else {
            for (const opp of status.bestOpportunities.slice(0, 5)) {
                console.log(`   ${opp.symbol}: ${opp.buyExchange} → ${opp.sellExchange} (${formatPercentage(opp.netProfitPercentage)})`);
            }
        }
        console.log('\n📈 性能统计:');
        console.log(`   检测到的机会: ${status.stats.opportunitiesDetected}`);
        console.log(`   执行的交易: ${status.stats.opportunitiesExecuted}`);
        console.log(`   成功率: ${formatPercentage(status.stats.winRate)}`);
        console.log(`   总利润: ${formatCurrency(status.stats.totalProfit)}`);
        console.log(`   净利润: ${formatCurrency(status.stats.netProfit)}`);
    }
    /**
     * 启动状态更新
     */
    startStatusUpdates() {
        setInterval(() => {
            if (!this.isShuttingDown && this.engine?.isEngineRunning()) {
                console.clear();
                console.log('🔄 跨所套利程序 - 实时监控');
                console.log('='.repeat(50));
                this.displayStatus();
                console.log('\n按 Ctrl+C 退出程序');
            }
        }, 5000); // 每5秒更新一次
    }
    /**
     * 优雅关闭
     */
    async gracefulShutdown(signal) {
        if (this.isShuttingDown) {
            return;
        }
        this.isShuttingDown = true;
        logger.info(`🛑 收到 ${signal} 信号，开始优雅关闭`);
        try {
            if (this.engine) {
                await this.engine.stop();
            }
            logger.info('✅ 程序已安全关闭');
            process.exit(0);
        }
        catch (error) {
            logger.error('❌ 关闭过程中发生错误', error);
            process.exit(1);
        }
    }
}
/**
 * 主函数
 */
async function main() {
    const app = new ArbitrageApp();
    await app.start();
}
// 启动应用
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch((error) => {
        logger.error('应用启动失败', error);
        process.exit(1);
    });
}
