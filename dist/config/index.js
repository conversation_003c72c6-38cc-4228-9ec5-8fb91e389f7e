/**
 * 默认套利配置
 */
export const defaultConfig = {
    exchanges: [
        {
            id: 'binance',
            name: 'Binance',
            enableRateLimit: true,
            rateLimit: 1200,
            fees: {
                trading: {
                    maker: 0.001,
                    taker: 0.001
                }
            }
        },
        {
            id: 'okx',
            name: 'OKX',
            enableRateLimit: true,
            rateLimit: 1000,
            fees: {
                trading: {
                    maker: 0.0008,
                    taker: 0.001
                }
            }
        },
        {
            id: 'bybit',
            name: 'Bybit',
            enableRateLimit: true,
            rateLimit: 1000,
            fees: {
                trading: {
                    maker: 0.001,
                    taker: 0.001
                }
            }
        }
    ],
    symbols: [
        'BTC/USDT',
        'ETH/USDT',
        'BNB/USDT',
        'ADA/USDT',
        'SOL/USDT',
        'XRP/USDT',
        'DOGE/USDT',
        'AVAX/USDT',
        'MATIC/USDT',
        'DOT/USDT'
    ],
    risk: {
        maxPositionSize: 1000, // USDT
        minProfitPercentage: 0.1, // 0.1%
        maxSlippage: 0.05, // 0.05%
        maxConcurrentTrades: 3,
        blacklistedSymbols: [],
        maxDailyLoss: 500, // USDT
        stopLossPercentage: 2.0 // 2%
    },
    monitoring: {
        priceUpdateInterval: 100, // ms
        opportunityCheckInterval: 50, // ms
        websocketReconnectDelay: 5000 // ms
    },
    trading: {
        enabled: false, // 默认禁用实际交易
        dryRun: true,
        orderTimeout: 30000, // 30 seconds
        retryAttempts: 3
    }
};
/**
 * 加载配置
 */
export function loadConfig() {
    // 从环境变量或配置文件加载配置
    const config = { ...defaultConfig };
    // 从环境变量加载 API 密钥
    config.exchanges = config.exchanges.map(exchange => ({
        ...exchange,
        apiKey: process.env[`${exchange.id.toUpperCase()}_API_KEY`],
        secret: process.env[`${exchange.id.toUpperCase()}_SECRET`],
        password: process.env[`${exchange.id.toUpperCase()}_PASSWORD`],
        sandbox: process.env.NODE_ENV !== 'production'
    }));
    // 从环境变量覆盖其他配置
    if (process.env.TRADING_ENABLED === 'true') {
        config.trading.enabled = true;
        config.trading.dryRun = false;
    }
    if (process.env.MIN_PROFIT_PERCENTAGE) {
        config.risk.minProfitPercentage = parseFloat(process.env.MIN_PROFIT_PERCENTAGE);
    }
    if (process.env.MAX_POSITION_SIZE) {
        config.risk.maxPositionSize = parseFloat(process.env.MAX_POSITION_SIZE);
    }
    if (process.env.SYMBOLS) {
        config.symbols = process.env.SYMBOLS.split(',').map(s => s.trim());
    }
    return config;
}
/**
 * 验证配置
 */
export function validateConfig(config) {
    const errors = [];
    // 验证交易所配置
    if (config.exchanges.length < 2) {
        errors.push('至少需要配置两个交易所');
    }
    // 验证交易对
    if (config.symbols.length === 0) {
        errors.push('至少需要配置一个交易对');
    }
    // 验证风险参数
    if (config.risk.minProfitPercentage <= 0) {
        errors.push('最小利润百分比必须大于0');
    }
    if (config.risk.maxPositionSize <= 0) {
        errors.push('最大仓位大小必须大于0');
    }
    if (config.risk.maxConcurrentTrades <= 0) {
        errors.push('最大并发交易数必须大于0');
    }
    // 如果启用实际交易，验证 API 密钥
    if (config.trading.enabled && !config.trading.dryRun) {
        for (const exchange of config.exchanges) {
            if (!exchange.apiKey || !exchange.secret) {
                errors.push(`交易所 ${exchange.name} 缺少 API 密钥配置`);
            }
        }
    }
    return errors;
}
