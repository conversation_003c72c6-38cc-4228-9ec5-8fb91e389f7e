import { EventEmitter } from 'events';
import { ArbitrageOpportunity, MarketSnapshot, RiskConfig, ExchangeConfig } from '../types/index.js';
/**
 * 套利机会检测器
 */
export declare class OpportunityDetector extends EventEmitter {
    private exchangeConfigs;
    private riskConfig;
    private exchangeFees;
    private lastOpportunities;
    constructor(exchangeConfigs: ExchangeConfig[], riskConfig: RiskConfig);
    /**
     * 初始化交易所手续费
     */
    private initializeExchangeFees;
    /**
     * 检测套利机会
     */
    detectOpportunities(snapshots: Map<string, MarketSnapshot>): ArbitrageOpportunity[];
    /**
     * 检测单个交易对的套利机会
     */
    private detectSymbolOpportunities;
    /**
     * 计算套利机会
     */
    private calculateOpportunity;
    /**
     * 过滤套利机会
     */
    private filterOpportunities;
    /**
     * 发出机会事件
     */
    private emitOpportunityEvent;
    /**
     * 获取最佳机会
     */
    getBestOpportunities(limit?: number): ArbitrageOpportunity[];
    /**
     * 获取特定交易对的机会
     */
    getSymbolOpportunities(symbol: string): ArbitrageOpportunity[];
    /**
     * 清理过期机会
     */
    cleanupExpiredOpportunities(maxAge?: number): void;
    /**
     * 获取统计信息
     */
    getStats(): {
        totalOpportunities: number;
        symbolStats: Map<string, number>;
        exchangePairStats: Map<string, number>;
        averageProfitPercentage: number;
    };
}
