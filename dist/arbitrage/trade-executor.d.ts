import { EventEmitter } from 'events';
import { ArbitrageOpportunity, ArbitrageTrade, ExchangeConfig, RiskConfig } from '../types/index.js';
/**
 * 交易执行引擎
 */
export declare class TradeExecutor extends EventEmitter {
    private exchangeConfigs;
    private riskConfig;
    private tradingConfig;
    private exchanges;
    private activeTrades;
    private concurrencyLimiter;
    private dailyLoss;
    private dailyResetTime;
    constructor(exchangeConfigs: ExchangeConfig[], riskConfig: RiskConfig, tradingConfig: {
        enabled: boolean;
        dryRun: boolean;
        orderTimeout: number;
        retryAttempts: number;
    });
    /**
     * 初始化交易所实例
     */
    private initializeExchanges;
    /**
     * 执行套利交易
     */
    executeArbitrage(opportunity: ArbitrageOpportunity): Promise<ArbitrageTrade | null>;
    /**
     * 内部交易执行逻辑
     */
    private executeTradeInternal;
    /**
     * 创建订单
     */
    private createOrder;
    /**
     * 取消订单
     */
    private cancelOrder;
    /**
     * 监控交易执行
     */
    private monitorTrade;
    /**
     * 等待订单完成
     */
    private waitForOrderCompletion;
    /**
     * 处理交易超时
     */
    private handleTradeTimeout;
    /**
     * 计算实际利润
     */
    private calculateActualProfit;
    /**
     * 检查风险限制
     */
    private checkRiskLimits;
    /**
     * 重置每日损失
     */
    private resetDailyLoss;
    /**
     * 获取活跃交易
     */
    getActiveTrades(): ArbitrageTrade[];
    /**
     * 获取交易统计
     */
    getStats(): {
        activeTrades: number;
        dailyLoss: number;
        maxConcurrentTrades: number;
    };
}
