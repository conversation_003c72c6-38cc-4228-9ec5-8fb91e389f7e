import { EventEmitter } from 'events';
import { OpportunityDetector } from './opportunity-detector.js';
import { TradeExecutor } from './trade-executor.js';
import { ArbitrageConfig, ArbitrageOpportunity, ArbitrageTrade, PerformanceStats } from '../types/index.js';
/**
 * 套利引擎主类
 */
export declare class ArbitrageEngine extends EventEmitter {
    private config;
    private priceMonitor;
    private opportunityDetector;
    private tradeExecutor;
    private isRunning;
    private monitoringInterval?;
    private cleanupInterval?;
    private stats;
    constructor(config: ArbitrageConfig);
    /**
     * 设置事件处理器
     */
    private setupEventHandlers;
    /**
     * 启动套利引擎
     */
    start(): Promise<void>;
    /**
     * 停止套利引擎
     */
    stop(): Promise<void>;
    /**
     * 启动定期检查
     */
    private startPeriodicChecks;
    /**
     * 检测套利机会
     */
    private checkOpportunities;
    /**
     * 执行套利机会
     */
    private executeOpportunity;
    /**
     * 更新交易统计
     */
    private updateTradeStats;
    /**
     * 获取当前状态
     */
    getStatus(): {
        isRunning: boolean;
        connections: Map<string, boolean>;
        activeTrades: ArbitrageTrade[];
        bestOpportunities: ArbitrageOpportunity[];
        stats: PerformanceStats;
    };
    /**
     * 获取特定交易对的机会
     */
    getSymbolOpportunities(symbol: string): ArbitrageOpportunity[];
    /**
     * 获取市场快照
     */
    getMarketSnapshot(symbol: string): import("../types/index.js").MarketSnapshot | null;
    /**
     * 获取所有市场快照
     */
    getAllMarketSnapshots(): Map<string, import("../types/index.js").MarketSnapshot>;
    /**
     * 手动执行套利机会
     */
    manualExecute(opportunityId: string): Promise<ArbitrageTrade | null>;
    /**
     * 获取详细统计信息
     */
    getDetailedStats(): {
        engine: PerformanceStats;
        opportunities: ReturnType<OpportunityDetector['getStats']>;
        trades: ReturnType<TradeExecutor['getStats']>;
    };
    /**
     * 重置统计信息
     */
    resetStats(): void;
    /**
     * 检查引擎是否正在运行
     */
    isEngineRunning(): boolean;
}
