/**
 * 交易所配置接口
 */
export interface ExchangeConfig {
    id: string;
    name: string;
    apiKey?: string;
    secret?: string;
    password?: string;
    sandbox?: boolean;
    rateLimit?: number;
    enableRateLimit?: boolean;
    fees?: {
        trading?: {
            maker?: number;
            taker?: number;
        };
    };
}
/**
 * 价格数据接口
 */
export interface PriceData {
    exchange: string;
    symbol: string;
    bid: number;
    ask: number;
    timestamp: number;
    datetime: string;
}
/**
 * 套利机会接口
 */
export interface ArbitrageOpportunity {
    id: string;
    symbol: string;
    buyExchange: string;
    sellExchange: string;
    buyPrice: number;
    sellPrice: number;
    spread: number;
    spreadPercentage: number;
    netProfit: number;
    netProfitPercentage: number;
    volume: number;
    timestamp: number;
    fees: {
        buyFee: number;
        sellFee: number;
        totalFees: number;
    };
}
/**
 * 交易订单接口
 */
export interface ArbitrageOrder {
    id: string;
    opportunityId: string;
    exchange: string;
    symbol: string;
    side: 'buy' | 'sell';
    amount: number;
    price: number;
    status: 'pending' | 'open' | 'closed' | 'canceled' | 'failed';
    orderId?: string;
    timestamp: number;
    error?: string;
}
/**
 * 套利交易对接口
 */
export interface ArbitrageTrade {
    id: string;
    opportunityId: string;
    symbol: string;
    buyOrder: ArbitrageOrder;
    sellOrder: ArbitrageOrder;
    status: 'pending' | 'partial' | 'completed' | 'failed' | 'canceled';
    actualProfit?: number;
    actualProfitPercentage?: number;
    startTime: number;
    endTime?: number;
    duration?: number;
}
/**
 * 风险控制配置
 */
export interface RiskConfig {
    maxPositionSize: number;
    minProfitPercentage: number;
    maxSlippage: number;
    maxConcurrentTrades: number;
    blacklistedSymbols: string[];
    whitelistedSymbols?: string[];
    maxDailyLoss: number;
    stopLossPercentage: number;
}
/**
 * 套利配置
 */
export interface ArbitrageConfig {
    exchanges: ExchangeConfig[];
    symbols: string[];
    risk: RiskConfig;
    monitoring: {
        priceUpdateInterval: number;
        opportunityCheckInterval: number;
        websocketReconnectDelay: number;
    };
    trading: {
        enabled: boolean;
        dryRun: boolean;
        orderTimeout: number;
        retryAttempts: number;
    };
}
/**
 * 市场数据快照
 */
export interface MarketSnapshot {
    symbol: string;
    exchanges: Map<string, PriceData>;
    lastUpdate: number;
}
/**
 * 性能统计
 */
export interface PerformanceStats {
    totalTrades: number;
    successfulTrades: number;
    failedTrades: number;
    totalProfit: number;
    totalFees: number;
    netProfit: number;
    averageProfit: number;
    winRate: number;
    averageExecutionTime: number;
    opportunitiesDetected: number;
    opportunitiesExecuted: number;
}
/**
 * WebSocket 事件类型
 */
export type WebSocketEvent = {
    type: 'price_update';
    data: PriceData;
} | {
    type: 'opportunity_detected';
    data: ArbitrageOpportunity;
} | {
    type: 'trade_executed';
    data: ArbitrageTrade;
} | {
    type: 'error';
    data: {
        exchange: string;
        error: string;
    };
} | {
    type: 'connection_status';
    data: {
        exchange: string;
        connected: boolean;
    };
};
/**
 * 日志级别
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
/**
 * 日志条目
 */
export interface LogEntry {
    timestamp: number;
    level: LogLevel;
    message: string;
    data?: any;
    exchange?: string;
    symbol?: string;
    opportunityId?: string;
    tradeId?: string;
}
